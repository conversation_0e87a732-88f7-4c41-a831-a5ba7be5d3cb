# Zustand 仿真状态管理迁移

## 概述

本次迁移将项目中的仿真相关状态从 props 传递方式改为使用 Zustand 全局状态管理。这提高了状态管理的效率和可维护性。

## 迁移内容

### 1. 创建了 Zustand Store

**文件**: `src/store/simulationStore.ts`

管理以下状态：
- `simulationRunning`: 仿真运行状态
- `isLoading`: 全屏loading状态
- `simulationConstellationName`: 仿真开始时的星座名称
- `currentSatelliteName`: 当前卫星名称

### 2. 更新的组件

#### `src/components/main/main.tsx`
- 移除了本地状态变量：
  - `const [simulationRunning, setSimulationRunning]`
  - `const [isLoading, setIsLoading]`
  - `const [simulationConstellationName, setSimulationConstellationName]`
  - `const [currentSatelliteName, setCurrentSatelliteName]`
- 使用 Zustand store 中的状态和 actions
- 使用 `resetSimulation()` 替代多个状态重置调用

#### `src/components/Header/settingsatellite.tsx`
- 移除了从 props 接收的仿真状态
- 使用 Zustand store 管理仿真状态
- 修复了所有相关的状态更新调用

#### `src/components/left/satelliteInfoList.tsx`
- 移除了从 props 接收的仿真状态：
  - `constellationName`
  - `simulationRunning`
  - `isLoading`
- 直接从 Zustand store 获取这些状态

### 3. 类型定义更新

**文件**: `src/components/types/type.ts`

从 `settingPanelProps` 类型中移除了：
- `simulationRunning?: boolean`
- `setSimulationRunning?: SetState<boolean>`

## 使用方法

### 在组件中使用 Zustand store

```typescript
import useSimulationStore from '../../store/simulationStore';

const MyComponent = () => {
  const {
    simulationRunning,
    isLoading,
    simulationConstellationName,
    currentSatelliteName,
    setSimulationRunning,
    setIsLoading,
    setSimulationConstellationName,
    setCurrentSatelliteName,
    resetSimulation
  } = useSimulationStore();

  // 使用状态
  console.log('仿真运行状态:', simulationRunning);

  // 更新状态
  const startSimulation = () => {
    setSimulationRunning(true);
  };

  // 重置所有状态
  const resetAllStates = () => {
    resetSimulation();
  };

  return (
    // 组件JSX
  );
};
```

## 优势

1. **减少 Props 传递**: 不再需要通过多层组件传递仿真状态
2. **集中管理**: 所有仿真相关状态在一个地方管理
3. **更好的性能**: 只有真正使用状态的组件才会重新渲染
4. **更容易维护**: 状态逻辑集中，便于调试和修改
5. **类型安全**: TypeScript 提供完整的类型检查

## 注意事项

1. 确保安装了正确版本的 Zustand (`^5.0.6`)
2. 新组件应直接使用 Zustand store 而不是通过 props 接收仿真状态
3. 使用 `resetSimulation()` 来重置所有仿真相关状态
4. 在开发环境中可以通过 Zustand DevTools 来调试状态变化

## 验证迁移成功

1. 启动应用，仿真功能应正常工作
2. 在不同组件中点击"开始仿真"/"停止仿真"按钮，状态应同步更新
3. 卫星信息列表应根据仿真状态正确显示/隐藏数据
4. Loading 状态应在所有组件中同步显示

## 后续改进建议

1. 可以考虑添加更多全局状态到 store 中，如卫星列表、基站列表等
2. 添加 Zustand 持久化插件以保存用户选择
3. 添加状态变化的日志记录用于调试 