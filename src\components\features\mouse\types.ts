// 鼠标事件处理器的依赖参数类型定义
export interface MouseHandlerDependencies {
  viewer: any;
  setCurSatellite: (satellite: string) => void;
  setNowSystemDate: (dates: string[]) => void;
  setSatellitePostionData: (data: number[]) => void;
  wgs84ToCartesignWrapper: (lng: any, lat: any, alt: any) => void;
  nowPicksatellite: { current: any };
  setPickedObject: (id: string | null, name: string | null) => void;
  beamCellManager: React.MutableRefObject<any>; // 整合管理器
  selectedSatelliteEntity: React.MutableRefObject<any>;
}

// 左键点击事件参数类型
export interface LeftClickHandlerParams {
  viewer: any;
  setCurSatellite: (satellite: string) => void;
  wgs84ToCartesignWrapper: (lng: any, lat: any, alt: any) => void;
  nowPicksatellite: { current: any };
  setPickedObject: (id: string | null, name: string | null) => void;
  beamCellManager: React.MutableRefObject<any>; // 整合管理器
  selectedSatelliteEntity: React.MutableRefObject<any>;
}

// 右键点击事件参数类型
export interface RightClickHandlerParams {
  viewer: any;
  setNowSystemDate: (dates: string[]) => void;
  setSatellitePostionData: (data: number[]) => void;
  nowPicksatellite: { current: any };
  beamCellManager: React.MutableRefObject<any>; // 整合管理器
  selectedSatelliteEntity: React.MutableRefObject<any>;
}

// 鼠标移动事件参数类型
export interface MouseMoveHandlerParams {
  viewer: any;
} 