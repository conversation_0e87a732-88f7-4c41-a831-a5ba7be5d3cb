/**
 * Processing Delay API Service
 * 获取处理延迟数据的接口服务
 */

/**
 * Processing Delay 响应接口定义
 */
export interface ProcessingDelayResponse {
  data: {
    processing_delay: number;
  };
  message: string;
  status: number;
}

/**
 * 获取处理延迟数据
 * @returns Promise<number> 返回处理延迟值，保留六位小数
 */
export async function getProcessingDelay(): Promise<number> {
  try {
    // console.log('调用处理延迟接口...');
    
    const response = await fetch('http://10.176.26.78:5000/api/get_processing_delay', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ProcessingDelayResponse = await response.json();
    
    if (data.status === 200 && data.data) {
      const processingDelay = data.data.processing_delay;
      // 保留六位小数
      const formattedDelay = Number(processingDelay.toFixed(6));
      // console.log('处理延迟数据获取成功:', formattedDelay);
      return formattedDelay;
    } else {
      console.warn('处理延迟接口返回错误:', data.message);
      return 0;
    }
  } catch (error) {
    console.error('获取处理延迟时出错:', error);
    return 0;
  }
}

/**
 * 获取处理延迟数据的简化版本
 * 直接返回格式化后的延迟值字符串
 * @returns Promise<string> 返回格式化的处理延迟字符串
 */
export async function getFormattedProcessingDelay(): Promise<string> {
  try {
    const delay = await getProcessingDelay();
    return delay.toFixed(6);
  } catch (error) {
    console.error('获取格式化处理延迟失败:', error);
    return '0.000000';
  }
}
