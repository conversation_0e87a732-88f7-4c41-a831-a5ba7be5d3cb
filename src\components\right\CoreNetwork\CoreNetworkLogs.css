.core-network-logs-wrapper {
  width: 100%;
  max-width: 20vw; /* 与左侧面板宽度保持一致 */
  height: 100%;
  max-height: 40vh;
  overflow: hidden; /* 外层容器隐藏溢出 */
  display: flex;
  flex-direction: column;
}

.core-network-logs {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #ffffff;
  font-size: clamp(10px, 1vw, 16px);
  overflow: hidden;
  background: transparent;
  position: relative;
}

.log-tabs {
  margin-bottom: 1%;
  width: 100% !important;
  flex-shrink: 0; /* 防止tabs被压缩 */
}

/* 确保Tab容器有足够空间 */
.log-tabs .ant-tabs-nav-wrap {
  overflow: visible !important;
  width: 100% !important;
}

/* 定制Tabs样式以匹配项目风格 */
.log-tabs .ant-tabs-nav {
  margin-bottom: 0.5%;
  overflow: visible !important; /* 防止出现省略号 */
}

/* 强制禁用Tab省略功能 */
.log-tabs .ant-tabs-nav-operations {
  display: none !important; /* 隐藏操作按钮（包括省略号） */
}

.log-tabs .ant-tabs-nav-more {
  display: none !important; /* 隐藏更多按钮 */
}
.log-tabs .ant-tabs-nav-list {
  display: flex !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
  overflow: visible !important; /* 防止出现省略号 */
}

.log-tabs .ant-tabs-tab {
  flex: 1; /* 让每个 Tab 占据相等空间 */
  text-align: center; /* 文字居中 */
  padding: 4px 8px !important; /* 固定内边距 */
  color: #ffffff !important;
  font-size: 12px !important; /* 固定字体大小 */
  background: rgba(13, 126, 222, 0.3) !important;
  border: 1px solid rgba(13, 126, 222, 0.6) !important;
  border-radius: 4px;
  margin-right: 4px !important; /* 固定间距 */
  transition: background-color 0.2s ease, border-color 0.2s ease;
  min-width: 0 !important; /* 允许收缩 */
  max-width: none !important; /* 移除最大宽度限制 */
}

.log-tabs .ant-tabs-tab:hover {
  color: #ffffff !important;
  background: rgba(13, 126, 222, 0.5) !important;
  border-color: rgba(13, 126, 222, 0.8) !important;
}

.log-tabs .ant-tabs-tab-active {
  color: #ffffff !important;
  background: rgba(13, 126, 222, 0.7) !important;
  border-color: rgba(13, 126, 222, 1) !important;
}

.log-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ffffff !important;
}

/* 确保Tab文字完整显示 */
.log-tabs .ant-tabs-tab-btn {
  color: #ffffff !important;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important; /* 禁用省略号 */
  width: auto !important;
}

.log-tabs .ant-tabs-nav::before {
  border-bottom: 1px solid rgba(13, 126, 222, 0.6);
}

.logs-container {
  flex: 1;
  overflow: auto; /* 同时支持水平和垂直滚动 */
  padding: min(0.5vw, 8px);
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(13, 126, 222, 0.4);
  border-radius: 4px;
  /* 设置最小尺寸以确保滚动区域 */
  min-height: 200px;
  max-height: 25vh; /* 限制最大高度 */
  scrollbar-width: thin;
  scrollbar-color: rgba(13, 126, 222, 0.6) rgba(255, 255, 255, 0.1);
}

.log-entry {
  display: flex;
  padding: min(0.4vw, 6px) min(0.6vw, 8px);
  margin-bottom: min(0.4vw, 6px);
  border-radius: 3px;
  background: rgba(0, 20, 40, 0.4);
  border-left: 3px solid transparent;
  font-family: 'Courier New', monospace;
  transition: background-color 0.2s ease;
  font-size: clamp(8px, 0.85vw, 13px);
  line-height: 1.2;
  color: #ffffff;
  /* 防止内容被压缩 */
  min-width: max-content;
  white-space: nowrap; /* 防止文字换行，确保水平滚动 */
}

.log-entry:hover {
  background: rgba(13, 126, 222, 0.3);
}

.log-info {
  border-left-color: #00d4ff;
}

.log-warning {
  border-left-color: #ffa500;
}

.log-error {
  border-left-color: #ff4757;
}

.log-time {
  flex: 0 0 auto;
  color: #00d4ff;
  margin-right: min(1vw, 14px);
  font-weight: bold;
  white-space: nowrap;
  min-width: 180px; /* 确保时间列有足够宽度 */
}

.log-message {
  flex: 1;
  color: #ffffff;
  white-space: nowrap; /* 防止消息换行 */
  overflow: visible; /* 允许内容溢出以支持水平滚动 */
  min-width: 300px; /* 确保消息有最小宽度 */
}

/* 添加媒体查询以便在不同尺寸屏幕上调整字体大小 */
@media screen and (max-width: 1200px) {
  .core-network-logs-wrapper {
    max-width: 18vw; /* 小屏幕时减少宽度 */
  }
  
  .core-network-logs {
    font-size: 12px;
  }
  
  .log-tabs .ant-tabs-tab {
    font-size: 11px;
  }
  
  .log-entry {
    font-size: 10px;
  }
  
  .log-time {
    min-width: 150px; /* 小屏幕时减少时间列宽度 */
  }
  
  .log-message {
    min-width: 250px; /* 小屏幕时减少消息列宽度 */
  }
}

@media screen and (max-width: 768px) {
  .core-network-logs-wrapper {
    max-width: 25vw; /* 更小屏幕时适当增加宽度 */
  }
  
  .core-network-logs {
    font-size: 10px;
  }
  
  .log-tabs .ant-tabs-tab {
    font-size: 9px;
  }
  
  .log-entry {
    font-size: 8px;
  }
  
  .log-time {
    min-width: 120px; /* 更小屏幕时进一步减少时间列宽度 */
  }
  
  .log-message {
    min-width: 200px; /* 更小屏幕时减少消息列宽度 */
  }
}

/* 自定义滚动条样式 */
.logs-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: rgba(13, 126, 222, 0.6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(13, 126, 222, 0.8);
}

/* 滚动条角落 */
.logs-container::-webkit-scrollbar-corner {
  background: rgba(0, 20, 40, 0.6);
}

/* 防止Tab抖动的特殊样式 */
.core-network-logs .ant-tabs {
  animation: none !important;
  transform: none !important;
}

.core-network-logs .ant-tabs-nav {
  animation: none !important;
  transform: none !important;
  overflow: visible !important;
}

.core-network-logs .ant-tabs-tab {
  animation: none !important;
  transform: none !important;
  will-change: auto !important;
}

/* 全局禁用省略号功能 */
.core-network-logs .ant-tabs-nav-operations {
  display: none !important;
}

.core-network-logs .ant-tabs-nav-more {
  display: none !important;
}

.core-network-logs .ant-tabs-nav-wrap {
  overflow: visible !important;
}

.core-network-logs .ant-tabs-tab-btn {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

.core-network-logs .ant-tabs-content-holder {
  background: transparent !important;
  animation: none !important;
  transform: none !important;
}

.core-network-logs .ant-tabs-tabpane {
  background: transparent !important;
  color: #ffffff !important;
  animation: none !important;
  transform: none !important;
}

/* 增强日志级别的视觉效果 */
.log-info .log-message {
  color: #ffffff;
}

.log-warning .log-message {
  color: #ffa500;
}

.log-error .log-message {
  color: #ff4757;
}

/* 移除可能导致抖动的动画效果 */  