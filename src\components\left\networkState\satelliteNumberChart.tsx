import React, { useEffect, useRef, useState, useCallback } from "react";
import * as echarts from "echarts";
import { UPDATE_FREQUENCY, getE2EStatus } from "./networkStateAPI";
import useSimulationStore, { SimulationState } from '../../../store/simulationStore';

const SatelliteNumberChart: React.FC<{}> = () => {
  // 从 store 获取状态
  const simulationRunning = useSimulationStore((state: SimulationState) => state.simulationRunning);
  const isLoading = useSimulationStore((state: SimulationState) => state.isLoading);
  
  // 图表相关的 refs 和状态
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.EChartsType | null>(null);
  const dataFetchTimer = useRef<NodeJS.Timeout | null>(null);

  // 图表数据状态
  const [latencyList, setLatencyList] = useState<number[]>(Array(25).fill(0));
  const [packetLossList, setPacketLossList] = useState<number[]>(Array(25).fill(0));
  const [timeLabels, setTimeLabels] = useState<(string | null)[]>(Array(25).fill(null));

  // 判断是否应该显示图表
  const shouldShowChart = simulationRunning && !isLoading;

  // 安全清理图表实例的函数
  const safeDisposeChart = useCallback(() => {
    if (chartInstance.current) {
      try {
        // 检查图表实例是否还有效
        if (!chartInstance.current.isDisposed()) {
          chartInstance.current.dispose();
        }
      } catch (error) {
        console.warn("图表清理时出现警告:", error);
      } finally {
        chartInstance.current = null;
        console.log("图表实例已清理");
      }
    }
  }, []);

  // 初始化图表
  useEffect(() => {
    if (shouldShowChart && chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
      console.log("图表初始化完成");
    }

    // 当不应该显示图表时，清理图表实例
    if (!shouldShowChart && chartInstance.current) {
      safeDisposeChart();
    }
  }, [shouldShowChart, safeDisposeChart]);

  // 数据获取函数
  const fetchData = useCallback(() => {
    // 只有在仿真运行且不在加载状态时才获取数据
    if (!shouldShowChart) {
      return;
    }

    // 调用 getE2EStatus() 获取真实的端到端状态数据
    try {
      let needUpdate = true;

      setTimeLabels((prev) => {
        const currentCesiumTime = useSimulationStore.getState().cesiumTime;
        if (!currentCesiumTime || !useSimulationStore.getState().simulationRunning || useSimulationStore.getState().isLoading) {
          needUpdate = false;
          return prev;
        }
        const date = new Date(currentCesiumTime);
        const currentLabel = date.toLocaleTimeString('en-GB', { hour12: false, timeZone: 'UTC' });
        const lastLabel = prev[prev.length - 1];
        if (currentLabel === lastLabel) {
          needUpdate = false;
          return prev;
        }
        return [...prev.slice(1), currentLabel];
      });

      if (needUpdate) {
        // 调用后端API获取真实数据
        getE2EStatus()
          .then((data) => {
            setLatencyList((prev) => [...prev.slice(1), data.latency]);
            setPacketLossList((prev) => [...prev.slice(1), data.packet_loss_rate]);
          })
          .catch((error) => {
            console.warn("获取E2E状态数据失败，使用模拟数据:", error);
            // 如果API调用失败，使用模拟数据作为备选
            const simulatedLatency = Math.random() * 100 + 50; // 50-150ms
            const simulatedPacketLoss = Math.random() * 5; // 0-5%
            setLatencyList((prev) => [...prev.slice(1), simulatedLatency]);
            setPacketLossList((prev) => [...prev.slice(1), simulatedPacketLoss]);
          });
      }
    } catch (error) {
      console.warn("更新图表数据时出错:", error);
    }
  }, [shouldShowChart]);

  // 清理定时器的函数
  const clearDataTimer = useCallback(() => {
    if (dataFetchTimer.current) {
      clearInterval(dataFetchTimer.current);
      dataFetchTimer.current = null;
      console.log("数据获取定时器已清理");
    }
  }, []);

  // 数据获取定时器管理
  useEffect(() => {
    if (shouldShowChart) {
      // 立即获取一次数据
      fetchData();
      // 设置定时器
      dataFetchTimer.current = setInterval(fetchData, UPDATE_FREQUENCY);
      console.log("数据获取定时器已启动");
    } else {
      // 清理定时器
      clearDataTimer();
    }

    return () => {
      clearDataTimer();
    };
  }, [shouldShowChart, fetchData, clearDataTimer]);

  // 图表配置选项
  const getChartOption = useCallback(() => {
    // 计算延迟数据的动态范围
    const validLatencyData = latencyList.filter(val => val > 0);
    let latencyMin = 0;
    let latencyMax = 100;

    if (validLatencyData.length > 0) {
      const minLatency = Math.min(...validLatencyData);
      const maxLatency = Math.max(...validLatencyData);
      const range = maxLatency - minLatency;
      const padding = Math.max(range * 0.1, 10); // 10%的边距，最少10ms

      latencyMin = Math.max(0, Math.floor(minLatency - padding));
      latencyMax = Math.ceil(maxLatency + padding);
    }

    return {
    // 背景颜色
    backgroundColor: "rgba(255,255,255, 0)",

    // 鼠标悬停显示
    tooltip: {
      trigger: "axis",
      backgroundColor: "transparent",
      textStyle: {
        color: "#fff",
        fontSize: 11,
      },
      axisPointer: {
        lineStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(126,199,255,0)" },
              { offset: 0.5, color: "rgba(126,199,255,1)" },
              { offset: 1, color: "rgba(126,199,255,0)" },
            ],
            global: false,
          },
        },
      },
    },

    // 图例
    legend: {
      align: "left",
      top: "3%",
      type: "plain",
      textStyle: {
        color: "white",
        fontSize: 12,
      },
      itemGap: 25,
      itemWidth: 20,
      icon: "path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",
      data: [
        { name: "latency" },
        { name: "loss rate" },
      ],
    },

    // 网格留白
    grid: {
      top: "16%",
      left: "10%",
      right: "10%",
      bottom: "15%",
    },

    // 横轴线
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLine: {
        show: true,
        lineStyle: { color: "rgba(255,255,255,0.5)" },
      },
      axisLabel: {
        textStyle: { color: "#fff", padding: 2, fontSize: 11 },
        formatter: function (data: any) { return data || ""; },
      },
      splitLine: {
        show: true,
        lineStyle: { color: "rgba(255,255,255,0.1)" },
      },
      axisTick: { show: false },
      data: timeLabels,
    },

    // 纵轴线
    yAxis: [
      {
        name: "latency/ms",
        nameTextStyle: { color: "#fff", padding: -5 },
        position: "left",
        min: latencyMin,
        max: latencyMax,
        splitLine: { show: false, lineStyle: { color: "rgba(255,255,255,0.1)" } },
        axisLine: { show: true, lineStyle: { color: "rgba(255,255,255,0.5)" } },
        axisLabel: { show: true, textStyle: { color: "#fff", padding: 2 } },
        axisTick: { show: false },
      },
      {
        name: "loss rate/%",
        nameTextStyle: { color: "#fff", padding: -5 },
        position: "right",
        max: 100,
        splitLine: { show: true, lineStyle: { color: "rgba(255,255,255,0.1)" } },
        axisLine: { show: true, lineStyle: { color: "rgba(255,255,255,0.5)" } },
        axisLabel: { show: true, textStyle: { color: "#fff", padding: 2 } },
        axisTick: { show: false },
      },
    ],
    series: [
      {
        name: "latency",
        yAxisIndex: 0,
        type: "line",
        symbol: "circle",
        showAllSymbol: true,
        symbolSize: 0,
        smooth: true,
        lineStyle: { normal: { width: 0.9, color: "rgba(13, 126, 222, 1)" }, borderColor: "rgba(0,0,0,.4)" },
        itemStyle: { color: "rgba(13, 126, 222, 1)", borderWidth: 0 },
        tooltip: { show: true },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: "rgba(13, 126, 222,.3)" },
                { offset: 1, color: "rgba(13, 126, 222, 0)" },
              ],
              false
            ),
            shadowColor: "rgba(13, 126, 222, 0.5)",
            shadowBlur: 20,
          },
        },
        data: latencyList,
      },
      {
        name: "loss rate",
        yAxisIndex: 1,
        type: "line",
        symbol: "circle",
        showAllSymbol: true,
        symbolSize: 0,
        smooth: true,
        lineStyle: { normal: { width: 0.9, color: "rgba(210, 51, 90,1)" }, borderColor: "rgba(0,0,0,.4)" },
        itemStyle: { color: "rgba(210, 51, 90,1)", borderWidth: 0 },
        tooltip: { show: true },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: "rgba(210, 51, 90,.3)" },
                { offset: 1, color: "rgba(210, 51, 90, 0)" },
              ],
              false
            ),
            shadowColor: "rgba(210, 51, 90, 0.5)",
            shadowBlur: 20,
          },
        },
        data: packetLossList,
      },
    ],
    };
  }, [timeLabels, latencyList, packetLossList]);

  // 更新图表数据
  useEffect(() => {
    if (shouldShowChart && chartInstance.current) {
      try {
        // 检查图表实例是否还有效
        if (!chartInstance.current.isDisposed()) {
          const option = getChartOption();
          chartInstance.current.setOption(option);
        }
      } catch (error) {
        console.warn("图表更新时出现警告:", error);
        // 如果图表更新失败，尝试重新初始化
        safeDisposeChart();
        if (chartRef.current) {
          chartInstance.current = echarts.init(chartRef.current);
          const option = getChartOption();
          chartInstance.current.setOption(option);
        }
      }
    }
  }, [shouldShowChart, getChartOption, safeDisposeChart]);

  // 组件清理
  useEffect(() => {
    return () => {
      // 清理定时器
      clearDataTimer();
      // 安全清理图表实例
      safeDisposeChart();
      console.log("组件清理完成");
    };
  }, [clearDataTimer, safeDisposeChart]);

  // 渲染逻辑
  if (!shouldShowChart) {
    return (
      <>
        <style>
          {`
            #network-state {
              height: 18vh;
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              color: white;
              font-size: 14px;
              text-align: center;
              background: rgba(0, 0, 0, 0.1);
              border-radius: 4px;
            }
          `}
        </style>
        <div id="network-state">
          {isLoading ? "数据加载中..." : "请先点击'开始仿真'按钮开始数据模拟"}
        </div>
      </>
    );
  }

  return (
    <>
      <style>
        {`
          #network-state {
            height: 18vh;
            width: 100%;
          }
        `}
      </style>
      <div id="network-state" ref={chartRef}></div>
    </>
  );
};

export default SatelliteNumberChart;