import React, { useState } from 'react';
import { Button } from 'antd';
import AddSatellite from './index';

// 演示组件，用于测试新的定时接收功能
const AddSatelliteDemo: React.FC = () => {
  const [visible, setVisible] = useState(false);

  const handleOpen = () => {
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleSubmit = (data: any) => {
    console.log('提交的数据:', data);
    
    if (data.type === 'scheduled') {
      console.log('定时接收任务设置成功:', data);
    } else if (data.type === 'file') {
      console.log('文件导入成功:', data);
    } else if (data.type === 'template') {
      console.log('模版构建成功:', data);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>星座管理演示</h2>
      <p>点击下面的按钮打开星座管理对话框，现在包含三种方式：</p>
      <ul>
        <li><strong>星座文件导入</strong>：上传TLE和ISL文件</li>
        <li><strong>星座模版构建</strong>：通过参数配置创建星座</li>
        <li><strong>定时接收</strong>：设置定时从URL获取TLE数据</li>
      </ul>
      
      <Button type="primary" onClick={handleOpen}>
        打开星座管理
      </Button>

      <AddSatellite
        visible={visible}
        onClose={handleClose}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default AddSatelliteDemo;
