.headerButton {
  background: rgba(255, 255, 255, 0.00); /* 初始背景透明 */
  border: none;
  color: white;
  font-size: 0.85vw;
  padding: 0.6rem 1.2rem;
  cursor: pointer;
  text-shadow: 0 0 6px rgb(255, 255, 255); /* 发光文字边缘 */
  transition: 
    background 0.4s ease,
    color 0.4s ease,
    text-shadow 0.4s ease,
    transform 0.1s ease;
  border-radius: 8px;
  backdrop-filter: blur(2px);
}

.headerButton:hover {
  background: rgba(0, 183, 255, 0.08); /* 更柔和的蓝色背景 */
  color: #ffffff;
  text-shadow: 0 0 12px rgba(0, 183, 255, 0.6), 0 0 24px rgba(0, 183, 255, 0.4); /* 柔光文字发光 */
  box-shadow: 0 0 20px rgba(0, 183, 255, 0.2); /* 柔和光晕边框 */
}

.headerButton:active {
  transform: scale(0.95); /* 点击缩小反馈 */
  background: rgba(0, 183, 255, 0.3);
}

.headerButton:focus {
  outline: none;
} 