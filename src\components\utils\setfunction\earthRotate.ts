// @ts-nocheck
// import * as Cesium from 'cesium';

// 定义地球旋转参数接口
export interface EarthRotateParams {
  viewer: any;
  previousTime: { current: number };
}

// 地球旋转函数
export const earthRotate = (params: EarthRotateParams): void => {
  const { viewer, previousTime } = params;
  
  var spinRate = 1;
  var currentTime = viewer.clock.currentTime.secondsOfDay;
  // console.log('当前时间为：',currentTime);
  var delta = (currentTime - previousTime.current) / 1000;
  previousTime.current = currentTime;
  viewer.scene.camera.rotate(Cesium.Cartesian3.UNIT_Z, -spinRate * delta);
}; 