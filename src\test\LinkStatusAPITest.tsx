import React, { useState, useEffect } from 'react';
import {
  getLinkStatus,
  getProcessedLinkStatus,
  LinkStatusResponse,
  ProcessedLinkStatus
} from '../components/features/mouse/linkStatusAPI';
import { LINK_STATUS_TEST_UPDATE_INTERVAL } from '../components/config/timerConfig';

/**
 * 链路状态API测试组件
 */
const LinkStatusAPITest: React.FC = () => {
  const [rawData, setRawData] = useState<LinkStatusResponse | null>(null);
  const [processedData, setProcessedData] = useState<ProcessedLinkStatus | null>(null);
  const [directData, setDirectData] = useState<ProcessedLinkStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 测试原始API调用
  const testRawAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getLinkStatus();
      setRawData(data);
      console.log('原始API数据:', data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(`原始API调用失败: ${errorMessage}`);
      console.error('原始API调用失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 测试处理后的API调用
  const testProcessedAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getProcessedLinkStatus();
      setProcessedData(data);
      console.log('处理后的API数据:', data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(`处理后的API调用失败: ${errorMessage}`);
      console.error('处理后的API调用失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 测试直接API调用（模拟鼠标悬停）
  const testDirectAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getProcessedLinkStatus();
      setDirectData(data);
      console.log('直接API数据:', data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(`直接API调用失败: ${errorMessage}`);
      console.error('直接API调用失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 自动刷新测试（模拟悬停时的定时更新）
  useEffect(() => {
    const interval = setInterval(() => {
      if (directData) {
        testDirectAPI();
      }
    }, LINK_STATUS_TEST_UPDATE_INTERVAL); // 从配置文件获取刷新间隔，默认2000ms

    return () => clearInterval(interval);
  }, [directData]);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2 style={{ color: '#00ff00' }}>链路状态API测试</h2>
      
      {/* 控制按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testRawAPI} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '8px 16px' }}
        >
          测试原始API
        </button>
        <button 
          onClick={testProcessedAPI} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '8px 16px' }}
        >
          测试处理后API
        </button>
        <button
          onClick={testDirectAPI}
          disabled={loading}
          style={{ marginRight: '10px', padding: '8px 16px' }}
        >
          测试直接API (模拟悬停)
        </button>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div style={{ color: '#ffff00', marginBottom: '10px' }}>
          正在加载...
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div style={{ 
          color: '#ff0000', 
          backgroundColor: 'rgba(255, 0, 0, 0.1)', 
          padding: '10px', 
          borderRadius: '5px',
          marginBottom: '20px'
        }}>
          {error}
        </div>
      )}

      {/* 原始数据显示 */}
      {rawData && (
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#00ff00' }}>原始API数据:</h3>
          <div style={{ 
            backgroundColor: 'rgba(0, 0, 0, 0.8)', 
            color: 'white', 
            padding: '10px', 
            borderRadius: '5px',
            fontFamily: 'monospace'
          }}>
            <pre>{JSON.stringify(rawData, null, 2)}</pre>
          </div>
        </div>
      )}

      {/* 处理后数据显示 */}
      {processedData && (
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#00ff00' }}>处理后的数据:</h3>
          <div style={{ 
            backgroundColor: 'rgba(0, 0, 0, 0.8)', 
            color: 'white', 
            padding: '10px', 
            borderRadius: '5px'
          }}>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>信号强度：</span>
              {processedData.signalStrength}
            </div>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>链路负载：</span>
              {processedData.linkLoadPercentage}
            </div>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>可用性：</span>
              <span style={{ color: processedData.linkStatusColor }}>
                {processedData.linkStatus}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* 直接API数据显示 */}
      {directData && (
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#00ff00' }}>直接API数据 (模拟悬停，每2秒自动刷新):</h3>
          <div style={{
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px'
          }}>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>信号强度：</span>
              {directData.signalStrength}
            </div>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>链路负载：</span>
              {directData.linkLoadPercentage}
            </div>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ color: '#ffff00' }}>可用性：</span>
              <span style={{ color: directData.linkStatusColor }}>
                {directData.linkStatus}
              </span>
            </div>
            <div style={{ fontSize: '12px', color: '#888888', marginTop: '10px' }}>
              最后更新: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div style={{ 
        backgroundColor: 'rgba(0, 255, 0, 0.1)', 
        padding: '15px', 
        borderRadius: '5px',
        marginTop: '20px'
      }}>
        <h4 style={{ color: '#00ff00', marginTop: 0 }}>使用说明:</h4>
        <ul style={{ color: '#ffffff', lineHeight: '1.6' }}>
          <li><strong>原始API:</strong> 直接调用后端API获取原始数据</li>
          <li><strong>处理后API:</strong> 获取并格式化数据用于显示</li>
          <li><strong>直接API:</strong> 模拟鼠标悬停时的API调用行为</li>
          <li><strong>自动刷新:</strong> 模拟悬停时每2秒自动更新一次</li>
        </ul>
      </div>
    </div>
  );
};

export default LinkStatusAPITest;
