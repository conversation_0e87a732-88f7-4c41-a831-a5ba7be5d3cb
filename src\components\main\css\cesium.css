.cesium-button {
    /* background: #303336;
    border: 1px solid #444;
    color: #edffff;
    fill: #edffff;
    border-radius: 4px;
    padding: 5px 12px;
    margin: 10px 10px;
    cursor: pointer;
    overflow: hidden;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline:0 none !important;
    z-index:999; */
    /* padding: 1em; */
    border: 0;
    padding: 0;
    border-radius: 0.5em;
    /* font-size: 0.8rem; */
    color: white !important;
    background-image: linear-gradient(to bottom, #57b, #148) !important;
    box-shadow: 0.1em 0.1em 0.5em #124 !important; 
}
.cesium-button:focus,
.cesium-button:active:focus,
.cesium-button.active:focus,
.cesium-button.focus,
.cesium-button:active.focus,
.cesium-button.active.focus{
    /* outline: none;
    border-color: transparent;
    box-shadow:none; */
    box-shadow: inset 0 0 0.5em #124, inset 0 0.5em 1em rgba(0,0,0,0.4);
}


.cesium-button-edit{
    border: 0;
    padding: 0;
    border-radius: 0.5em;
    /* font-size: 0.8rem; */
    color: white !important;
    background-image: linear-gradient(to bottom, #57b, #148) !important;
    box-shadow: 0.1em 0.1em 0.5em #124 !important; 
    float: right;
    margin-right: 3.3vw;
    margin-top: 0.1vh;
    cursor: pointer;
    z-index: 1000;
    position: relative;
}

.ant-table-tbody > tr > td {
    text-align: center !important;
}

.ant-table-cell:nth-child(1){
    text-align: left !important;
}

.editButton{
    background-image: url("../assets//resources/edit.png");
    background-position: center;
    height: 32px;
    width: 32px;
}

.ant-table, .ant-select-selector, .ant-pagination-item-link{
    color: #fff !important;
    background: rgba(0, 20, 40, 0.6) !important;
}

.ant-table-cell{
    color: #ffffff !important;
    background: rgba(0, 20, 40, 0.4) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

.ant-table-tbody > tr > td {
    background: rgba(0, 20, 40, 0.4) !important;
    color: #ffffff !important;
}

.ant-table-thead > tr > th{
    background: rgba(13, 126, 222, 0.8) !important;
    color: #ffffff !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.ant-table-tbody>tr>td{
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.ant-table-tbody>tr:hover>td{
    background: rgba(13, 126, 222, 0.3) !important;
}

.cesium-btn-selected{
    background: #4488bb !important;
}

#toolbar{
    width: 100vw;
    height: 3vh;
    margin-top: 6px;
    top: 5px;
    position: absolute;
    padding-top: 5px;
    padding-left: 34vw;
    z-index: 1000;
}

.settingList{
    list-style: none;
    color: #fff;
    font-size: 16px;
}

.settingList li{
    margin-bottom: 10px;
}

.ant-input-number-handler-wrap, .ant-input-number-input-wrap{
    background-color: #262c33 !important;
    color: #fff;
    border-color: #fff !important;
}

.settingPanel{
    width: 6vw;
    display: block;
    position: absolute;
    box-sizing: content-box;
    top: 4vh;
    right: 1.5vw;
    max-height: 500px;
    margin-top: 5px;
    background-color: rgba(38, 38, 38, 0.75);
    border: 1px solid #444;
    padding: 6px;
    overflow: auto;
    border-radius: 10px;
    z-index: 999;
    color: #fff;
    font-size: 18px;
}

.settingPanel ul{
    padding: 8px;
}

.settingPanel ul li {
    list-style: none;
    margin-top: 2px;
    padding-left: 12px;
}

.fade {
    -webkit-user-select: none;
    user-select: none;
    transform: translate(0, -20%);
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s 0.2s, opacity 0.2s ease-in, transform 0.2s ease-in;
}

.sceneEdit {
    width: 50vw !important;
}

/* Modal关闭符号白色样式 */
.sceneEdit .ant-modal-close-x,
.sceneEdit .ant-modal-close-icon {
    color: #fff !important;
}

.sceneEditTitle{
    color: #fff !important;
    font-size: 18px !important;
}

.ant-modal-title {
    color: #fff !important;
    font-size: 24px !important;
}

.ant-modal-header{
    background-color: #2c333d !important;
    border: none !important;
}

.ant-list{
    max-height: 100vh !important;
    padding-top: 1vh !important; 
    overflow-y: scroll !important;
    color: #fff !important;
}

.ant-modal-content {
    background-color: #21292f !important;
}

.ant-modal-footer{
    border-top: 2px solid #2c333d !important;
}

.ant-list-item{
    border-bottom: 2px solid #2c333d !important;
    padding: 0!important;
}

.scenceSetting{
    padding-top: 1vh;
}

.checkItem{
    margin-right: 16px;

}

.ant-table-tbody>tr{
    height: 3.3vh !important;
}

.ant-pagination{
    display: none !important;
}

.satelliteList{
    position: absolute !important;
    float: left;
    top: 5vh;
    left: 2.7vw;
    width: 12vw;
    height: 95%;
    padding-top: 0vh;
    padding-left: 10px;
    background: #353536;
    color: #fff !important;
    z-index: 999;
}

#title {
    color: rgba(13, 126, 222, 1);
    position: absolute;
    z-index: 999;
    left: 10%;
    font-weight: 600;
    letter-spacing: 3px;
    font-size: 40px;
    transform: translate(-50%);
}

.box-title {
    width: 100%;
    height: 2vh;
    z-index: 999;
    background-size:contain;
    background-repeat: no-repeat;
    background-image: url("../assets/title_con.png");
}

.cesium-viewer-animationContainer, .cesium-viewer-timelineContainer{
    z-index: 999;
}

.left-wrap{
    width: 22vw;
    height: 90vh;
    left: 5px;
    top: 0px;
    padding-top: 8vh;
    padding-left: 1.5vw;
    position: absolute;
    z-index: 999;
    /* background: linear-gradient(to right, rgba(0,0,0,0.3), transparent); */
}

.leftMask{
    position: absolute;
    left: 0;
    top:0;
    width: 30vw;
    height: 100vh;
    background: linear-gradient(to right, #000,#0000);
}

.rightMask{
    position: absolute;
    right: 0;
    top:0;
    width: 45vw;
    height: 100vh;
    background: linear-gradient(to left, #000,#0000);
}

.right-wrap{
    width: 22vw;
    height: 92vh; /* 减少高度，为padding-top留出空间 */
    right: 1.5vw;
    top: 0px;
    padding-top: 8vh;
    padding-left: 1.5vw;
    position: absolute;
    z-index: 999;
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* 添加垂直滚动 */
    /* background: linear-gradient(to left, rgba(0,0,0,0.3), transparent); */
}


.bottom-wrap{
    width: 50vw;
    height: 8vh;
    left: 25vw;
    bottom: 3vh;
    position: absolute;
}

.box-title-font {
    color: #08acbd;
    margin-left: 150px;
    letter-spacing: 2px;
    font-size: 18px;
    box-sizing: border-box;
}

.btnSelected{
    background:#4488bb
}
.cesium-viewer-bottom{
    display: none !important;
}

.cesium-viewer-toolbar{
    right: 5vw !important;
    margin-top: 1vh;
    z-index: 2000;
}

.right-box {
    width: 100%;
    height: 20vh;
    overflow: hidden;
    box-sizing: border-box;
}

.baseStation-wrap {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background-image: url("../assets/rightCon01.png");
    background-size: cover;
    background-repeat: no-repeat;
}



.baseStationIcon {
    width: 100%;
    height: 100%;
    background-image: url("../assets/basestation_work.png");
    background-size: 50%;
    background-repeat: no-repeat;
    background-position: center;
    border-bottom: 2px solid rgba(13, 126, 222, 1);
}

.dashboardIcon {
    width: 32px;
    height: 32px;
    float: right;
    margin-top: 28%;
    box-sizing: border-box;
    background-image: url("../assets/dot_32.png");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.6;
    transition: opacity 0.3s;
}

.dashboardIcon:hover{
    opacity: 1;
}

.baseStationText_title {
    color: rgba(13, 126, 222, 1);
    font-size: 16px;
    margin: 0;
}


.baseStationText_content{
    color: rgba(13, 126, 222, 1);
    font-size: 18px;
    border: 500;
    margin: 0;
    letter-spacing: 1px;
}

.ant-tag-success, .ant-tag-error{
    background-color: rgba(0, 20, 40, 0.6) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-size: 16px !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.ant-tag-success {
    border-color: rgba(82, 196, 26, 0.8) !important;
    background-color: rgba(82, 196, 26, 0.2) !important;
}

.ant-tag-error {
    border-color: rgba(255, 77, 79, 0.8) !important;
    background-color: rgba(255, 77, 79, 0.2) !important;
}

.row-style {
    width:"100%";
    height:80px;
    padding-left:8px;
    padding-top:5px; 
}

p {
    margin: 0;
}

#baseStationList::-webkit-scrollbar {
    width: 4px !important;
    height: 8px !important;
}

#baseStationList::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(223, 223, 223, 0.5) !important;
    background: #42484c79 !important;
}

#baseStationList::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px #08090b79 !important;
    border-radius: 2 !important;
    background: #10131679 !important;
}

#left-border-line, #right-border-line{
    background-image: url('../assets/header-line.png'); 
    background-repeat: no-repeat;
    background-size: contain;
    width:90vh;
    height:1vw;
    position: absolute;
    top:30px;
    
}

#left-border-line{
    transform: rotate(90deg); 
    transform-origin:left top;
    left: 1vw;
}
#right-border-line{
    transform: rotate(-90deg); 
    transform-origin:right top;
    right: 1vw;
}

#modal {
    z-index: 999;
}

.loadingScene {
    box-sizing: border-box;
    font-size: 18px;
    margin: 0;
    cursor: pointer;
}

.loadingScene:hover {
    color:#08acbd;
}

#top-edit{
    width: 100vw;
    height: 5vh;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    background: url("../assets//snap/header.png");
    background-size: contain;
    z-index: 999;
}

#left-edit{
    width: 380px;
    height: 1379px;
    position: absolute;
    left: 0;
    top:5vh;
    background: url("../assets/snap/left.png");
    background-size: contain;
    z-index: 999;
}

#bottom-edit{
    width: 1880px;
    height: 300px;
    position: absolute;
    left: 382px;
    bottom:0px;
    background: url("../assets/snap/bottom.png");
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 999;
    background-position: center;
}

#right-edit{
    width: 300px;
    height: 1379px;
    position: absolute;
    right: 0px;
    bottom:0px;
    background: url("../assets/snap/right.png");
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 995;
}

#show-light{
    position: absolute;
    top: 57.1vh;
    right: 1.9vw;
    z-index: 999;
}

#imageWrapper{
    background: url("../assets/huojian/huojian5.png");
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    height: 100%;
}

.ant-table-wrapper {
    max-height: 100% !important;
}

/* Added for SatelliteInfoList all table data cells styling */
.satellite-info-list-table-wrapper .ant-table-cell {
  color: #ffffff !important; /* 修改为白色 */
  font-size: 16px; /* Desired font-size */
  font-weight: bold; /* Desired font-weight */
}

