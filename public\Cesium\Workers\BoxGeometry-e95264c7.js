/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-c450597e","./Matrix2-21f90abf","./RuntimeError-cef79f54","./ComponentDatatype-4028c72d","./defaultValue-4607806f","./GeometryAttribute-3c090c07","./GeometryAttributes-acac33d2","./GeometryOffsetAttribute-3e5f3e97","./VertexFormat-75e8069c"],(function(t,e,n,a,r,i,o,m,u,s){"use strict";const y=new n.Cartesian3;function c(t){const e=(t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT)).minimum,a=t.maximum,r=i.defaultValue(t.vertexFormat,s.VertexFormat.DEFAULT);this._minimum=n.Cartesian3.clone(e),this._maximum=n.Cartesian3.clone(a),this._vertexFormat=r,this._offsetAttribute=t.offsetAttribute,this._workerName="createBoxGeometry"}c.fromDimensions=function(t){const e=(t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT)).dimensions,a=n.Cartesian3.multiplyByScalar(e,.5,new n.Cartesian3);return new c({minimum:n.Cartesian3.negate(a,new n.Cartesian3),maximum:a,vertexFormat:t.vertexFormat,offsetAttribute:t.offsetAttribute})},c.fromAxisAlignedBoundingBox=function(t){return new c({minimum:t.minimum,maximum:t.maximum})},c.packedLength=2*n.Cartesian3.packedLength+s.VertexFormat.packedLength+1,c.pack=function(t,e,a){return a=i.defaultValue(a,0),n.Cartesian3.pack(t._minimum,e,a),n.Cartesian3.pack(t._maximum,e,a+n.Cartesian3.packedLength),s.VertexFormat.pack(t._vertexFormat,e,a+2*n.Cartesian3.packedLength),e[a+2*n.Cartesian3.packedLength+s.VertexFormat.packedLength]=i.defaultValue(t._offsetAttribute,-1),e};const f=new n.Cartesian3,p=new n.Cartesian3,x=new s.VertexFormat,l={minimum:f,maximum:p,vertexFormat:x,offsetAttribute:void 0};let A;c.unpack=function(t,e,a){e=i.defaultValue(e,0);const r=n.Cartesian3.unpack(t,e,f),o=n.Cartesian3.unpack(t,e+n.Cartesian3.packedLength,p),m=s.VertexFormat.unpack(t,e+2*n.Cartesian3.packedLength,x),u=t[e+2*n.Cartesian3.packedLength+s.VertexFormat.packedLength];return i.defined(a)?(a._minimum=n.Cartesian3.clone(r,a._minimum),a._maximum=n.Cartesian3.clone(o,a._maximum),a._vertexFormat=s.VertexFormat.clone(m,a._vertexFormat),a._offsetAttribute=-1===u?void 0:u,a):(l.offsetAttribute=-1===u?void 0:u,new c(l))},c.createGeometry=function(t){const a=t._minimum,s=t._maximum,c=t._vertexFormat;if(n.Cartesian3.equals(a,s))return;const f=new m.GeometryAttributes;let p,x;if(c.position&&(c.st||c.normal||c.tangent||c.bitangent)){if(c.position&&(x=new Float64Array(72),x[0]=a.x,x[1]=a.y,x[2]=s.z,x[3]=s.x,x[4]=a.y,x[5]=s.z,x[6]=s.x,x[7]=s.y,x[8]=s.z,x[9]=a.x,x[10]=s.y,x[11]=s.z,x[12]=a.x,x[13]=a.y,x[14]=a.z,x[15]=s.x,x[16]=a.y,x[17]=a.z,x[18]=s.x,x[19]=s.y,x[20]=a.z,x[21]=a.x,x[22]=s.y,x[23]=a.z,x[24]=s.x,x[25]=a.y,x[26]=a.z,x[27]=s.x,x[28]=s.y,x[29]=a.z,x[30]=s.x,x[31]=s.y,x[32]=s.z,x[33]=s.x,x[34]=a.y,x[35]=s.z,x[36]=a.x,x[37]=a.y,x[38]=a.z,x[39]=a.x,x[40]=s.y,x[41]=a.z,x[42]=a.x,x[43]=s.y,x[44]=s.z,x[45]=a.x,x[46]=a.y,x[47]=s.z,x[48]=a.x,x[49]=s.y,x[50]=a.z,x[51]=s.x,x[52]=s.y,x[53]=a.z,x[54]=s.x,x[55]=s.y,x[56]=s.z,x[57]=a.x,x[58]=s.y,x[59]=s.z,x[60]=a.x,x[61]=a.y,x[62]=a.z,x[63]=s.x,x[64]=a.y,x[65]=a.z,x[66]=s.x,x[67]=a.y,x[68]=s.z,x[69]=a.x,x[70]=a.y,x[71]=s.z,f.position=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:x})),c.normal){const t=new Float32Array(72);t[0]=0,t[1]=0,t[2]=1,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=1,t[9]=0,t[10]=0,t[11]=1,t[12]=0,t[13]=0,t[14]=-1,t[15]=0,t[16]=0,t[17]=-1,t[18]=0,t[19]=0,t[20]=-1,t[21]=0,t[22]=0,t[23]=-1,t[24]=1,t[25]=0,t[26]=0,t[27]=1,t[28]=0,t[29]=0,t[30]=1,t[31]=0,t[32]=0,t[33]=1,t[34]=0,t[35]=0,t[36]=-1,t[37]=0,t[38]=0,t[39]=-1,t[40]=0,t[41]=0,t[42]=-1,t[43]=0,t[44]=0,t[45]=-1,t[46]=0,t[47]=0,t[48]=0,t[49]=1,t[50]=0,t[51]=0,t[52]=1,t[53]=0,t[54]=0,t[55]=1,t[56]=0,t[57]=0,t[58]=1,t[59]=0,t[60]=0,t[61]=-1,t[62]=0,t[63]=0,t[64]=-1,t[65]=0,t[66]=0,t[67]=-1,t[68]=0,t[69]=0,t[70]=-1,t[71]=0,f.normal=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}if(c.st){const t=new Float32Array(48);t[0]=0,t[1]=0,t[2]=1,t[3]=0,t[4]=1,t[5]=1,t[6]=0,t[7]=1,t[8]=1,t[9]=0,t[10]=0,t[11]=0,t[12]=0,t[13]=1,t[14]=1,t[15]=1,t[16]=0,t[17]=0,t[18]=1,t[19]=0,t[20]=1,t[21]=1,t[22]=0,t[23]=1,t[24]=1,t[25]=0,t[26]=0,t[27]=0,t[28]=0,t[29]=1,t[30]=1,t[31]=1,t[32]=1,t[33]=0,t[34]=0,t[35]=0,t[36]=0,t[37]=1,t[38]=1,t[39]=1,t[40]=0,t[41]=0,t[42]=1,t[43]=0,t[44]=1,t[45]=1,t[46]=0,t[47]=1,f.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:t})}if(c.tangent){const t=new Float32Array(72);t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t[6]=1,t[7]=0,t[8]=0,t[9]=1,t[10]=0,t[11]=0,t[12]=-1,t[13]=0,t[14]=0,t[15]=-1,t[16]=0,t[17]=0,t[18]=-1,t[19]=0,t[20]=0,t[21]=-1,t[22]=0,t[23]=0,t[24]=0,t[25]=1,t[26]=0,t[27]=0,t[28]=1,t[29]=0,t[30]=0,t[31]=1,t[32]=0,t[33]=0,t[34]=1,t[35]=0,t[36]=0,t[37]=-1,t[38]=0,t[39]=0,t[40]=-1,t[41]=0,t[42]=0,t[43]=-1,t[44]=0,t[45]=0,t[46]=-1,t[47]=0,t[48]=-1,t[49]=0,t[50]=0,t[51]=-1,t[52]=0,t[53]=0,t[54]=-1,t[55]=0,t[56]=0,t[57]=-1,t[58]=0,t[59]=0,t[60]=1,t[61]=0,t[62]=0,t[63]=1,t[64]=0,t[65]=0,t[66]=1,t[67]=0,t[68]=0,t[69]=1,t[70]=0,t[71]=0,f.tangent=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}if(c.bitangent){const t=new Float32Array(72);t[0]=0,t[1]=1,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=1,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=1,t[14]=0,t[15]=0,t[16]=1,t[17]=0,t[18]=0,t[19]=1,t[20]=0,t[21]=0,t[22]=1,t[23]=0,t[24]=0,t[25]=0,t[26]=1,t[27]=0,t[28]=0,t[29]=1,t[30]=0,t[31]=0,t[32]=1,t[33]=0,t[34]=0,t[35]=1,t[36]=0,t[37]=0,t[38]=1,t[39]=0,t[40]=0,t[41]=1,t[42]=0,t[43]=0,t[44]=1,t[45]=0,t[46]=0,t[47]=1,t[48]=0,t[49]=0,t[50]=1,t[51]=0,t[52]=0,t[53]=1,t[54]=0,t[55]=0,t[56]=1,t[57]=0,t[58]=0,t[59]=1,t[60]=0,t[61]=0,t[62]=1,t[63]=0,t[64]=0,t[65]=1,t[66]=0,t[67]=0,t[68]=1,t[69]=0,t[70]=0,t[71]=1,f.bitangent=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}p=new Uint16Array(36),p[0]=0,p[1]=1,p[2]=2,p[3]=0,p[4]=2,p[5]=3,p[6]=6,p[7]=5,p[8]=4,p[9]=7,p[10]=6,p[11]=4,p[12]=8,p[13]=9,p[14]=10,p[15]=8,p[16]=10,p[17]=11,p[18]=14,p[19]=13,p[20]=12,p[21]=15,p[22]=14,p[23]=12,p[24]=18,p[25]=17,p[26]=16,p[27]=19,p[28]=18,p[29]=16,p[30]=20,p[31]=21,p[32]=22,p[33]=20,p[34]=22,p[35]=23}else x=new Float64Array(24),x[0]=a.x,x[1]=a.y,x[2]=a.z,x[3]=s.x,x[4]=a.y,x[5]=a.z,x[6]=s.x,x[7]=s.y,x[8]=a.z,x[9]=a.x,x[10]=s.y,x[11]=a.z,x[12]=a.x,x[13]=a.y,x[14]=s.z,x[15]=s.x,x[16]=a.y,x[17]=s.z,x[18]=s.x,x[19]=s.y,x[20]=s.z,x[21]=a.x,x[22]=s.y,x[23]=s.z,f.position=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:x}),p=new Uint16Array(36),p[0]=4,p[1]=5,p[2]=6,p[3]=4,p[4]=6,p[5]=7,p[6]=1,p[7]=0,p[8]=3,p[9]=1,p[10]=3,p[11]=2,p[12]=1,p[13]=6,p[14]=5,p[15]=1,p[16]=2,p[17]=6,p[18]=2,p[19]=3,p[20]=7,p[21]=2,p[22]=7,p[23]=6,p[24]=3,p[25]=0,p[26]=4,p[27]=3,p[28]=4,p[29]=7,p[30]=0,p[31]=1,p[32]=5,p[33]=0,p[34]=5,p[35]=4;const l=n.Cartesian3.subtract(s,a,y),A=.5*n.Cartesian3.magnitude(l);if(i.defined(t._offsetAttribute)){const e=x.length,n=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(e/3).fill(n);f.applyOffset=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new o.Geometry({attributes:f,indices:p,primitiveType:o.PrimitiveType.TRIANGLES,boundingSphere:new e.BoundingSphere(n.Cartesian3.ZERO,A),offsetAttribute:t._offsetAttribute})},c.getUnitBox=function(){return i.defined(A)||(A=c.createGeometry(c.fromDimensions({dimensions:new n.Cartesian3(1,1,1),vertexFormat:s.VertexFormat.POSITION_ONLY}))),A},t.BoxGeometry=c}));
