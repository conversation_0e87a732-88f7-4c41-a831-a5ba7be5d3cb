// 卫星数据服务模块主入口
// 提供卫星和地面站数据获取的API调用功能

// 导入用于创建服务对象的模块
import { fetchSatelliteData, fetchGroundStationData, fetchBeamStatus } from './api';
import { UPDATE_FREQUENCY } from './constants';

// 导出类型定义
export type {
  Position,
  Velocity,
  SatelliteData,
  GroundStationData,
  BeamConnection
} from './types';

// 导出常量
export {
  UPDATE_FREQUENCY,
  EARTH_RADIUS_KM,
  METERS_TO_KM,
  DEFAULT_ALTITUDE_RANGE,
  VELOCITY_RANGE,
  POSITION_RANGE,
  MOCK_GROUND_STATIONS
} from './constants';

// 导出API函数
export {
  fetchSatelliteData,
  fetchGroundStationData,
  fetchBeamStatus
} from './api';

// 导出工具函数（如果需要在外部使用）
export {
  getCurrentTime,
  convertPosition,
  convertVelocity,
  calculateAltitude,
  formatAdjacentSatellites,
  formatConnectedGroundStations,
  transformBackendData,
  transformGroundStationData,
  generateMockSatelliteData,
  generateMockGroundStationData
} from './utils';

// 导出服务对象（保持向后兼容）
export const satelliteDataService = {
  fetchSatelliteData,
  fetchGroundStationData,
  fetchBeamStatus,
  UPDATE_FREQUENCY
};
