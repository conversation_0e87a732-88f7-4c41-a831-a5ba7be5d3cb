
import { basisBackendUrl } from '../../utils/api/basicURL';
const BACKEND_URL = basisBackendUrl

// --- 定时任务API ---
const scheduleTleUpdateUrl = basisBackendUrl + '/schedule_tle_update';
const cancelTleUpdateUrl = basisBackendUrl + '/cancel_tle_update';
/**
 * 卫星星座JSON数据的类型定义
 */
export interface SatelliteConstellationData {
  constellation_name: string;
  NUM_ORBS: number;
  NUM_SATS_PER_ORB: number;
  ALTITUDE_M: number;
  INCLINATION_DEGREE: number;
  MEAN_MOTION_REV_PER_DAY?: number;
  SATELLITE_CONE_RADIUS_M?: number;
  isl_config: string;
}

/**
 * API响应的类型定义
 */
export interface ApiResponse {
  success?: boolean;
  message?: string;
  error?: string;
  data?: any;
}


/**
 * 上传卫星星座JSON数据 (模版构建)
 * @param data 星座配置数据
 * @returns Promise<ApiResponse>
 */
export async function uploadSatelliteDataJson(data: SatelliteConstellationData): Promise<ApiResponse> {
  console.log('开始上传JSON数据:', data);
  console.log('上传URL:', `${BACKEND_URL}/upload_satellite_data_json`);
  
  try {
    console.log('发送fetch请求...');
    const response = await fetch(`${BACKEND_URL}/upload_satellite_data_json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      mode: 'cors',
      credentials: 'same-origin',
      body: JSON.stringify(data)
    });
    
    console.log('响应状态:', response.status);
    let result: ApiResponse;
    
    try {
      result = await response.json();
      console.log('响应数据:', result);
    } catch (e) {
      console.log('解析响应错误:', e);
      // 如果无法解析响应但状态OK，假设成功
      if (response.ok) {
        result = { success: true };
      } else {
        throw e;
      }
    }
    
    // 判断上传是否成功
    const isSuccess = result.success || (response.ok && !result.error);
    
    if (isSuccess) {
      console.log('上传成功');
      return { success: true, message: '星座配置上传成功' };
    } else {
      const errorMsg = result.message || result.error || '未知错误';
      console.error('上传失败，响应:', result);
      return { success: false, error: errorMsg };
    }
  } catch (error: any) {
    console.error('上传错误详情:', {
      error: error,
      message: error.message,
      stack: error.stack
    });
    
    // 尝试验证上传是否成功
    try {
      console.log('尝试验证上传状态...');
      // 等待后端处理
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const verifyResponse = await fetch(`${BACKEND_URL}/get_constellation_list`, {
        mode: 'cors',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      console.log('验证响应:', verifyResponse);
      const constellations = await verifyResponse.json();
      console.log('当前星座列表:', constellations);
      
      // 检查新上传的星座是否在列表中
      if (verifyResponse.ok && constellations.includes(data.constellation_name)) {
        console.log('验证成功 - 在列表中找到星座');
        return { success: true, message: '星座配置上传成功' };
      }
    } catch (verifyError: any) {
      console.error('验证错误详情:', {
        error: verifyError,
        message: verifyError.message,
        stack: verifyError.stack
      });
    }
    
    return { success: false, error: `上传失败: ${error.message}` };
  }
}

/**
 * 上传卫星数据文件 (文件导入)
 * @param formData 包含文件的FormData对象
 * @returns Promise<ApiResponse>
 */
export async function uploadSatelliteDataFiles(formData: FormData): Promise<ApiResponse> {
  // 验证必需的文件
  if (!formData.get('tles')) {
    return { success: false, error: '请选择星轨数据文件(TLE)' };
  }
  if (!formData.get('isls')) {
    return { success: false, error: '请选择拓扑数据文件(ISL)' };
  }
  
  // 检查波束状态文件
  const beamStatusFile = formData.get('beam_status');
  if (beamStatusFile) {
    console.log(`✓ 检测到波束状态文件: ${(beamStatusFile as File).name}`);
  } else {
    console.log('ℹ️ 未上传波束状态文件，所有卫星将默认为启用状态');
  }
  
  try {
    const response = await fetch(`${BACKEND_URL}/upload_satellite_data`, {
      method: 'POST',
      body: formData,
      mode: 'cors',
      credentials: 'same-origin',
      headers: {
        'Accept': 'application/json'
      }
    });
    
    const result = await response.json();
   
    if (response.ok && result.data && result.data.success) {
      return { 
        success: true, 
        message: `星座上传成功: ${result.data.message}` 
      };
    } else {
      const errorMessage = (result.data && result.data.message) || result.message || '未知错误';
      return { success: false, error: errorMessage };
    }
  } catch (error: any) {
    console.error('上传错误:', error);
    
    // 尝试验证上传是否成功
    try {
      // 等待后端处理
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const verifyResponse = await fetch(`${BACKEND_URL}/get_constellation_list`, {
        mode: 'cors',
        credentials: 'same-origin'
      });
      
      // 如果能获取星座列表，假设上传可能成功
      if (verifyResponse.ok) {
        return { 
          success: true, 
          message: '上传可能成功，尽管连接出现问题。正在刷新星座列表...' 
        };
      }
    } catch (verifyError: any) {
      console.error('验证错误:', verifyError);
    }
    
    return { success: false, error: `上传失败: ${error.message}` };
  }
}

/**
 * 获取星座列表 (用于验证上传结果)
 * @returns Promise<string[]>
 */
export async function getConstellationList(): Promise<string[]> {
  try {
    const response = await fetch(`${BACKEND_URL}/get_constellation_list`, {
      mode: 'cors',
      credentials: 'same-origin',
      headers: {
        'Accept': 'application/json'
      }
    });
    
    if (response.ok) {
      return await response.json();
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error: any) {
    console.error('获取星座列表失败:', error);
    throw error;
  }
}

/**
 * 验证JSON配置数据
 * @param data 待验证的数据
 * @returns 验证结果和错误信息
 */
export function validateJsonData(data: any): { isValid: boolean; error?: string } {
  // 字段验证范围
  const fieldValidation = {
    'NUM_ORBS': { min: 1, max: 100 },
    'NUM_SATS_PER_ORB': { min: 1, max: 100 },
    'ALTITUDE_M': { min: 200000, max: 2000000 }, // 200km to 2000km
    'INCLINATION_DEGREE': { min: 0, max: 180 },
    'MEAN_MOTION_REV_PER_DAY': { min: 1, max: 20 },
    'SATELLITE_CONE_RADIUS_M': { min: 100000, max: 2000000 }
  };
  
  // 验证必需字段
  const requiredFields = ['constellation_name', 'NUM_ORBS', 'NUM_SATS_PER_ORB', 'ALTITUDE_M', 'INCLINATION_DEGREE', 'isl_config'];
  for (let field of requiredFields) {
    if (data[field] === undefined || data[field] === '') {
      return { isValid: false, error: `请填写 ${field}` };
    }
  }
  
  // 验证数值字段的范围
  for (let [key, range] of Object.entries(fieldValidation)) {
    if (data[key] !== undefined) {
      const num = Number(data[key]);
      if (isNaN(num)) {
        return { isValid: false, error: `${key} 必须是数字` };
      }
      if (num < range.min || num > range.max) {
        return { isValid: false, error: `${key} 必须在 ${range.min} 和 ${range.max} 之间` };
      }
    }
  }
  
  return { isValid: true };
}

/**
 * 设置一个定时任务，从URL下载TLE数据并更新星座
 * @param {string} tleUrl TLE文件的URL
 * @param {number} intervalInSeconds 更新间隔（秒）
 * @returns {Promise<ApiResponse>}
 */
export async function scheduleTleUpdate(tleUrl: string, intervalInSeconds: number): Promise<ApiResponse> {
    try {
        const requestData = {
            tle_url: tleUrl,
            interval: intervalInSeconds,
            unit: 'seconds'
        };
        console.log(`Scheduling TLE update with:`, requestData);

        const response = await fetch(scheduleTleUpdateUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            mode: 'cors',
            credentials: 'same-origin',
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (response.ok && (result.success !== false)) {
            return {
                success: true,
                message: result.message || '定时任务设置成功',
                data: result
            };
        } else {
            return {
                success: false,
                error: result.message || result.error || '定时任务设置失败'
            };
        }
    } catch (error: any) {
        console.error('Schedule TLE update error:', error);
        return {
            success: false,
            error: `设置定时任务失败: ${error.message}`
        };
    }
}

/**
 * 取消已设置的TLE更新任务
 * @returns {Promise<ApiResponse>}
 */
export async function cancelTleUpdate(): Promise<ApiResponse> {
    try {
        console.log(`Cancelling TLE update task...`);

        const response = await fetch(cancelTleUpdateUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            mode: 'cors',
            credentials: 'same-origin',
            body: JSON.stringify({})
        });

        const result = await response.json();

        if (response.ok && (result.success !== false)) {
            return {
                success: true,
                message: result.message || '定时任务取消成功',
                data: result
            };
        } else {
            return {
                success: false,
                error: result.message || result.error || '定时任务取消失败'
            };
        }
    } catch (error: any) {
        console.error('Cancel TLE update error:', error);
        return {
            success: false,
            error: `取消定时任务失败: ${error.message}`
        };
    }
}