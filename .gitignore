# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

#resource


# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# logs
output.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# refactored folder (temporary exclusion)
src/components/main/refactored