// API 响应接口定义
interface ApiResponse<T = any> {
  status?: number;
  message?: string;
  data?: T;
}

// 错误响应接口
interface ApiError {
  status: number;
  message: string;
}

// const basisBackendUrl = config.config.backend.url + ":" + config.config.backend.port + "/api";
// const basisBackendUrl = config.config.backend.url + ":5001/api";
// const ipAddress = "************";
const ipAddress: string = "************";
// const ipAddress = "************";
// const ipAddress = "spr.randow.cn";
// const ipAddress = "sdt.randow.cn";

// const basisBackendUrl = "http://" + window.location.hostname + ":5001/api";
// const basisSSHUrl = "ws://" + window.location.hostname + ":";

const basisBackendUrl: string = "http://" + ipAddress + ":5001/api";
const basisSSHUrl: string = "ws://" + ipAddress + ":";

/**
 * 发送 GET 请求获取数据
 * @param url - 请求的 URL
 * @param timeout - 超时时间（毫秒），默认 1000000ms
 * @returns Promise<T> - 返回响应数据
 */
async function GET<T = any>(url: string, timeout: number = 1000000): Promise<T> {
    const controller = new AbortController(); // 创建 AbortController 实例
    // const timeoutId = setTimeout(() => controller.abort(), timeout); // 设置超时
  
    try {
      console.log(url);
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal, // 将信号传递给 fetch
      });

      const data: ApiResponse<T> = await response.json();
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${data.status}, Message: ${data.message}`);
      }
      if (data.data !== undefined) {
        return data.data; // 返回数据
      }
      return (data.message as unknown) as T; // 返回数据
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.error('Request timed out');
      } else {
        console.error('Failed to fetch ', url, ':', error);
      }
      throw error; // 重新抛出错误以便调用方处理
    } finally {
      // clearTimeout(timeoutId); // 清除超时定时器
    }
}
  
/**
 * 发送 POST 请求
 * @param url - 请求的 URL
 * @param requestData - 请求数据
 * @param timeout - 超时时间（毫秒），默认 1000000ms
 * @returns Promise<T> - 返回响应数据
 */
async function POST<T = any>(url: string, requestData?: any, timeout: number = 1000000): Promise<T> {
    const controller = new AbortController(); // 创建 AbortController 实例
    // const timeoutId = setTimeout(() => controller.abort(), timeout); // 设置超时逻辑
  
    try {
        const response = await fetch(url, {
            method: "POST", // 使用 POST 方法
            headers: {
                "Content-Type": "application/json" // 告知服务器数据为 JSON 格式
            },
            body: requestData ? JSON.stringify(requestData) : undefined, // 将请求数据转为 JSON 字符串
            signal: controller.signal // 传入 signal 参数以支持取消
        });

        const data: ApiResponse<T> = await response.json(); // 将响应数据解析为 JSON
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${data.status}, Message: ${data.message}`);
        }
        if (data.data !== undefined) {
            return data.data; // 返回数据
        }
        return (`${data.status} ${data.message}` as unknown) as T;
    } catch (error: any) {
        if (error.name === "AbortError") {
            console.error("Request timed out.");
        } else {
            console.error("Error accessing the backend:", error);
        }
        throw error;
    } finally {
      // clearTimeout(timeoutId); // 清除超时定时器
    }
}

export { basisBackendUrl, basisSSHUrl, GET, POST };
export type { ApiResponse, ApiError };
