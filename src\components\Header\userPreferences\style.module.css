/* 用户首选项模态框样式 */
.userPreferencesModal {
  /* 模态框整体样式 */
}

.userPreferencesModal .ant-modal-content {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.userPreferencesModal .ant-modal-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.userPreferencesModal .ant-modal-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.userPreferencesModal .ant-modal-close-x {
  color: white;
}

.userPreferencesModal .ant-modal-body {
  padding: 20px;
}

/* 标签页样式 */
.userPreferencesModal .ant-tabs {
  color: white;
}

.userPreferencesModal .ant-tabs-tab {
  color: rgba(255, 255, 255, 0.7);
}

.userPreferencesModal .ant-tabs-tab-active {
  color: white !important;
}

.userPreferencesModal .ant-tabs-ink-bar {
  background: #40a9ff;
}

.userPreferencesModal .ant-tabs-content-holder {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 20px;
  margin-top: 16px;
}

/* 标签页内容 */
.tabContent {
  color: white;
}

.section {
  margin-bottom: 32px;
}

.section h3 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

/* 状态信息样式 */
.statusInfo {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #40a9ff;
}

.statusInfo p {
  margin: 0;
  color: white;
  font-size: 14px;
}

.statusInfo strong {
  color: #40a9ff;
}

/* 表单样式 */
.form {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.formRow {
  display: flex;
  gap: 16px;
}

.formItem {
  flex: 1;
}

/* 表单控件样式 */
.userPreferencesModal .ant-form-item-label > label {
  color: white;
  font-weight: 500;
}

.userPreferencesModal .ant-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.userPreferencesModal .ant-input:hover {
  border-color: #40a9ff;
  background: rgba(255, 255, 255, 0.15);
}

.userPreferencesModal .ant-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.userPreferencesModal .ant-input-number {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 100%;
}

.userPreferencesModal .ant-input-number:hover {
  border-color: #40a9ff;
}

.userPreferencesModal .ant-input-number-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
}

.userPreferencesModal .ant-input-number-input {
  color: white;
}

.userPreferencesModal .ant-select {
  color: white;
}

.userPreferencesModal .ant-select-selector {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white;
}

.userPreferencesModal .ant-select-selection-item {
  color: white !important;
}

.userPreferencesModal .ant-select:hover .ant-select-selector {
  border-color: #40a9ff !important;
}

.userPreferencesModal .ant-select-focused .ant-select-selector {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2) !important;
}

/* 按钮样式 */
.submitButton {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border: none;
  height: 40px;
  font-weight: 600;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(64, 169, 255, 0.3);
}

.submitButton:hover {
  background: linear-gradient(135deg, #69c0ff 0%, #40a9ff 100%);
  box-shadow: 0 4px 12px rgba(64, 169, 255, 0.4);
}

/* 表格样式 */
.channelTable {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.userPreferencesModal .ant-table {
  background: transparent;
}

.userPreferencesModal .ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
}

.userPreferencesModal .ant-table-tbody > tr > td {
  background: transparent;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.userPreferencesModal .ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.05);
}

.userPreferencesModal .ant-table-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 操作按钮样式 */
.userPreferencesModal .ant-btn-link {
  color: #ff4d4f;
  padding: 0;
}

.userPreferencesModal .ant-btn-link:hover {
  color: #ff7875;
}

/* 确认弹窗样式 */
.userPreferencesModal .ant-popconfirm .ant-popover-inner {
  background: #1f1f1f;
  color: white;
}

.userPreferencesModal .ant-popconfirm .ant-popover-arrow {
  border-top-color: #1f1f1f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 0;
  }
  
  .userPreferencesModal {
    width: 95% !important;
    max-width: none !important;
  }
}

/* 加载状态 */
.userPreferencesModal .ant-spin-dot-item {
  background-color: #40a9ff;
}

/* 消息提示样式 */
.userPreferencesModal .ant-message {
  color: white;
}

/* 表单验证错误样式 */
.userPreferencesModal .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f;
}

.userPreferencesModal .ant-form-item-has-error .ant-input:hover {
  border-color: #ff4d4f;
}

.userPreferencesModal .ant-form-item-has-error .ant-input:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.userPreferencesModal .ant-form-item-explain-error {
  color: #ff7875;
}
