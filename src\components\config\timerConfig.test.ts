/**
 * 定时器配置测试文件
 * 验证定时器配置的正确性和完整性
 */

import {
  TIMER_CONFIG,
  DEFAULT_TIMER_CONFIG,
  MAIN_CLOCK_SYNC_INTERVAL,
  SATELLITE_LIST_UPDATE_INTERVAL,
  SATELLITE_DETAIL_UPDATE_INTERVAL,
  NETWORK_STATE_CHART_UPDATE_INTERVAL,
  LINK_STATUS_TEST_UPDATE_INTERVAL,
  LEGACY_SATELLITE_INFO_UPDATE_INTERVAL,
  MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL,
  CZML_CLEAR_DELAY,
  TEST_WAIT_DELAY,
  DEFAULT_API_TIMEOUT,
  getTimerInterval,
  getDelayTime,
  validateTimerConfig,
  TimerConfig
} from './timerConfig';

describe('定时器配置测试', () => {
  
  test('所有导出的常量应该有正确的值', () => {
    // 测试数据获取相关定时器
    expect(MAIN_CLOCK_SYNC_INTERVAL).toBe(500);
    expect(SATELLITE_LIST_UPDATE_INTERVAL).toBe(800);
    expect(SATELLITE_DETAIL_UPDATE_INTERVAL).toBe(800);
    expect(NETWORK_STATE_CHART_UPDATE_INTERVAL).toBe(200);
    expect(LINK_STATUS_TEST_UPDATE_INTERVAL).toBe(2000);
    expect(LEGACY_SATELLITE_INFO_UPDATE_INTERVAL).toBe(2000);
    expect(MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL).toBe(200);

    // 测试延迟相关定时器
    expect(CZML_CLEAR_DELAY).toBe(200);
    expect(TEST_WAIT_DELAY).toBe(300);
    
    // 测试API超时配置
    expect(DEFAULT_API_TIMEOUT).toBe(1000000);
  });

  test('配置对象应该包含所有必要的属性', () => {
    expect(TIMER_CONFIG).toBeDefined();
    expect(TIMER_CONFIG.dataFetch).toBeDefined();
    expect(TIMER_CONFIG.delays).toBeDefined();
    expect(TIMER_CONFIG.apiTimeouts).toBeDefined();
    
    // 验证数据获取配置
    expect(TIMER_CONFIG.dataFetch.MAIN_CLOCK_SYNC_INTERVAL).toBe(500);
    expect(TIMER_CONFIG.dataFetch.SATELLITE_LIST_UPDATE_INTERVAL).toBe(800);
    expect(TIMER_CONFIG.dataFetch.SATELLITE_DETAIL_UPDATE_INTERVAL).toBe(800);
    expect(TIMER_CONFIG.dataFetch.NETWORK_STATE_CHART_UPDATE_INTERVAL).toBe(200);
    expect(TIMER_CONFIG.dataFetch.LINK_STATUS_TEST_UPDATE_INTERVAL).toBe(2000);
    expect(TIMER_CONFIG.dataFetch.LEGACY_SATELLITE_INFO_UPDATE_INTERVAL).toBe(2000);
    expect(TIMER_CONFIG.dataFetch.MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL).toBe(200);

    // 验证延迟配置
    expect(TIMER_CONFIG.delays.CZML_CLEAR_DELAY).toBe(200);
    expect(TIMER_CONFIG.delays.TEST_WAIT_DELAY).toBe(300);
    
    // 验证API超时配置
    expect(TIMER_CONFIG.apiTimeouts.DEFAULT_API_TIMEOUT).toBe(1000000);
  });

  test('工具函数应该正常工作', () => {
    // 测试 getTimerInterval
    expect(getTimerInterval('MAIN_CLOCK_SYNC_INTERVAL')).toBe(500);
    expect(getTimerInterval('SATELLITE_LIST_UPDATE_INTERVAL')).toBe(800);
    expect(getTimerInterval('NETWORK_STATE_CHART_UPDATE_INTERVAL')).toBe(200);
    
    // 测试 getDelayTime
    expect(getDelayTime('CZML_CLEAR_DELAY')).toBe(200);
    expect(getDelayTime('TEST_WAIT_DELAY')).toBe(300);
  });

  test('配置验证函数应该正常工作', () => {
    // 测试有效配置
    const validConfig: TimerConfig = {
      dataFetch: {
        MAIN_CLOCK_SYNC_INTERVAL: 500,
        SATELLITE_LIST_UPDATE_INTERVAL: 800,
        SATELLITE_DETAIL_UPDATE_INTERVAL: 800,
        NETWORK_STATE_CHART_UPDATE_INTERVAL: 200,
        LINK_STATUS_TEST_UPDATE_INTERVAL: 2000,
        LEGACY_SATELLITE_INFO_UPDATE_INTERVAL: 2000,
        MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL: 200,
      },
      delays: {
        CZML_CLEAR_DELAY: 200,
        TEST_WAIT_DELAY: 300,
      },
      apiTimeouts: {
        DEFAULT_API_TIMEOUT: 1000000,
      },
    };
    
    const validResult = validateTimerConfig(validConfig);
    expect(validResult.valid).toBe(true);
    expect(validResult.errors).toHaveLength(0);
    
    // 测试无效配置
    const invalidConfig: TimerConfig = {
      dataFetch: {
        MAIN_CLOCK_SYNC_INTERVAL: -500, // 负数，应该无效
        SATELLITE_LIST_UPDATE_INTERVAL: 0, // 零，应该无效
        SATELLITE_DETAIL_UPDATE_INTERVAL: 800,
        NETWORK_STATE_CHART_UPDATE_INTERVAL: 200,
        LINK_STATUS_TEST_UPDATE_INTERVAL: 2000,
        LEGACY_SATELLITE_INFO_UPDATE_INTERVAL: 2000,
        MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL: 200,
      },
      delays: {
        CZML_CLEAR_DELAY: -200, // 负数，应该无效
        TEST_WAIT_DELAY: 300,
      },
      apiTimeouts: {
        DEFAULT_API_TIMEOUT: 0, // 零，应该无效
      },
    };
    
    const invalidResult = validateTimerConfig(invalidConfig);
    expect(invalidResult.valid).toBe(false);
    expect(invalidResult.errors.length).toBeGreaterThan(0);
  });

  test('默认配置应该通过验证', () => {
    const result = validateTimerConfig(DEFAULT_TIMER_CONFIG);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('所有定时器值应该是正数', () => {
    const config = DEFAULT_TIMER_CONFIG;
    
    // 检查数据获取定时器
    Object.values(config.dataFetch).forEach(value => {
      expect(value).toBeGreaterThan(0);
      expect(typeof value).toBe('number');
    });
    
    // 检查延迟定时器（应该是非负数）
    Object.values(config.delays).forEach(value => {
      expect(value).toBeGreaterThanOrEqual(0);
      expect(typeof value).toBe('number');
    });
    
    // 检查API超时配置
    Object.values(config.apiTimeouts).forEach(value => {
      expect(value).toBeGreaterThan(0);
      expect(typeof value).toBe('number');
    });
  });

});

// 导出测试用的配置，供其他测试文件使用
export const TEST_TIMER_CONFIG: TimerConfig = {
  dataFetch: {
    MAIN_CLOCK_SYNC_INTERVAL: 100, // 更快的测试间隔
    SATELLITE_LIST_UPDATE_INTERVAL: 200,
    SATELLITE_DETAIL_UPDATE_INTERVAL: 200,
    NETWORK_STATE_CHART_UPDATE_INTERVAL: 50,
    LINK_STATUS_TEST_UPDATE_INTERVAL: 500,
    LEGACY_SATELLITE_INFO_UPDATE_INTERVAL: 500,
    MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL: 100,
  },
  delays: {
    CZML_CLEAR_DELAY: 50,
    TEST_WAIT_DELAY: 100,
  },
  apiTimeouts: {
    DEFAULT_API_TIMEOUT: 5000, // 更短的测试超时
  },
};
