import { basisBackendUrl } from '../../utils/api/basicURL';

// API基础URL
const API_BASE_URL = basisBackendUrl;

// API响应类型定义
interface TimeConfigResponse {
  success: boolean;
  message?: string;
}

// 时间参数接口
interface TimeArguments {
  start_time: string;
  duration: number;
  time_interval: number;
}

/**
 * 验证时间格式是否为 YYYY-MM-DD HH:MM:SS
 * @param timeString 时间字符串
 * @returns boolean 是否符合格式
 */
function validateTimeFormat(timeString: string): boolean {
  const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  return timeRegex.test(timeString);
}

/**
 * 验证duration和time_interval是否大于0
 * @param value 数值
 * @returns boolean 是否大于0
 */
function validatePositiveNumber(value: number): boolean {
  return typeof value === 'number' && value > 0;
}

/**
 * 获取当前时间并格式化为 YYYY-MM-DD HH:MM:SS
 * @returns string 格式化的当前时间
 */
function getCurrentTimeString(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 将小时转换为纳秒
 * @param hours 小时数
 * @returns number 纳秒数
 */
function hoursToNanoseconds(hours: number): number {
  // 1小时 = 3600秒 = 3600 * 10^9 纳秒
  return hours * 3600 * 1000000000;
}

/**
 * 设置时间参数
 * @param endTimeHours 结束时间（小时）
 * @returns Promise<boolean> 是否成功
 */
export async function setTimeArguments(endTimeHours: number): Promise<boolean> {
  try {
    // 获取当前时间
    const startTime = getCurrentTimeString();
    
    // 将小时转换为纳秒
    const duration = hoursToNanoseconds(endTimeHours);
    
    // 固定的时间间隔（纳秒）
    const timeInterval = 200000000000;
    
    // 构建请求参数
    const timeArgs: TimeArguments = {
      start_time: startTime,
      duration: duration,
      time_interval: timeInterval
    };
    
    // 参数验证
    if (!validateTimeFormat(timeArgs.start_time)) {
      console.error('时间格式验证失败:', timeArgs.start_time);
      return false;
    }
    
    if (!validatePositiveNumber(timeArgs.duration)) {
      console.error('Duration验证失败:', timeArgs.duration);
      return false;
    }
    
    if (!validatePositiveNumber(timeArgs.time_interval)) {
      console.error('Time interval验证失败:', timeArgs.time_interval);
      return false;
    }
    
    console.log('发送时间参数:', timeArgs);
    
    // 发送请求
    const response = await fetch(`${API_BASE_URL}/set_time_arg`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(timeArgs),
    });

    const result: TimeConfigResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.message = 'Success') {
      console.log('时间参数设置成功:', {
        startTime: timeArgs.start_time,
        durationHours: endTimeHours,
        durationNanoseconds: timeArgs.duration,
        timeIntervalNanoseconds: timeArgs.time_interval
      });
      return true;
    } else {
      console.error('时间参数设置失败:', result.message);
      return false;
    }
    
  } catch (error) {
    console.error('设置时间参数失败:', error);
    return false;
  }
}

/**
 * 配置时间参数的包装函数
 * 提供更友好的接口调用方式
 * @param endTimeHours 结束时间（小时，从0开始计算）
 * @returns Promise<boolean> 是否成功
 */
export async function configureSimulationTime(endTimeHours: number): Promise<boolean> {
  console.log(`开始配置仿真时间参数，持续时间: ${endTimeHours} 小时`);
  
  if (endTimeHours <= 0) {
    console.error('结束时间必须大于0小时');
    return false;
  }
  
  if (endTimeHours > 24) {
    console.warn('结束时间超过24小时，请确认这是预期的设置');
  }
  
  const success = await setTimeArguments(endTimeHours);
  
  if (success) {
    // console.log('仿真时间参数配置完成');
  } else {
    console.error('仿真时间参数配置失败');
  }
  
  return success;
}

// 导出工具函数（供测试使用）
export { getCurrentTimeString, hoursToNanoseconds, validateTimeFormat, validatePositiveNumber }; 