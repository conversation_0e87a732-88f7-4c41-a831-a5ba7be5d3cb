/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-c450597e","./Matrix2-21f90abf","./ComponentDatatype-4028c72d","./defaultValue-4607806f","./RuntimeError-cef79f54","./EllipseGeometryLibrary-187266ea","./GeometryAttribute-3c090c07","./GeometryAttributes-acac33d2","./GeometryOffsetAttribute-3e5f3e97","./IndexDatatype-20e78e57"],(function(e,t,i,r,n,o,a,s,l,u,c){"use strict";const d=new i.Cartesian3;let f=new i.Cartesian3;const p=new t.BoundingSphere,m=new t.BoundingSphere;function h(e){const t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).center,o=n.defaultValue(e.ellipsoid,i.Ellipsoid.WGS84),a=e.semiMajorAxis,s=e.semiMinorAxis,l=n.defaultValue(e.granularity,r.CesiumMath.RADIANS_PER_DEGREE),u=n.defaultValue(e.height,0),c=n.defaultValue(e.extrudedHeight,u);this._center=i.Cartesian3.clone(t),this._semiMajorAxis=a,this._semiMinorAxis=s,this._ellipsoid=i.Ellipsoid.clone(o),this._rotation=n.defaultValue(e.rotation,0),this._height=Math.max(c,u),this._granularity=l,this._extrudedHeight=Math.min(c,u),this._numberOfVerticalLines=Math.max(n.defaultValue(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}h.packedLength=i.Cartesian3.packedLength+i.Ellipsoid.packedLength+8,h.pack=function(e,t,r){return r=n.defaultValue(r,0),i.Cartesian3.pack(e._center,t,r),r+=i.Cartesian3.packedLength,i.Ellipsoid.pack(e._ellipsoid,t,r),r+=i.Ellipsoid.packedLength,t[r++]=e._semiMajorAxis,t[r++]=e._semiMinorAxis,t[r++]=e._rotation,t[r++]=e._height,t[r++]=e._granularity,t[r++]=e._extrudedHeight,t[r++]=e._numberOfVerticalLines,t[r]=n.defaultValue(e._offsetAttribute,-1),t};const y=new i.Cartesian3,A=new i.Ellipsoid,_={center:y,ellipsoid:A,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};h.unpack=function(e,t,r){t=n.defaultValue(t,0);const o=i.Cartesian3.unpack(e,t,y);t+=i.Cartesian3.packedLength;const a=i.Ellipsoid.unpack(e,t,A);t+=i.Ellipsoid.packedLength;const s=e[t++],l=e[t++],u=e[t++],c=e[t++],d=e[t++],f=e[t++],p=e[t++],m=e[t];return n.defined(r)?(r._center=i.Cartesian3.clone(o,r._center),r._ellipsoid=i.Ellipsoid.clone(a,r._ellipsoid),r._semiMajorAxis=s,r._semiMinorAxis=l,r._rotation=u,r._height=c,r._granularity=d,r._extrudedHeight=f,r._numberOfVerticalLines=p,r._offsetAttribute=-1===m?void 0:m,r):(_.height=c,_.extrudedHeight=f,_.granularity=d,_.rotation=u,_.semiMajorAxis=s,_.semiMinorAxis=l,_.numberOfVerticalLines=p,_.offsetAttribute=-1===m?void 0:m,new h(_))},h.createGeometry=function(e){if(e._semiMajorAxis<=0||e._semiMinorAxis<=0)return;const o=e._height,h=e._extrudedHeight,y=!r.CesiumMath.equalsEpsilon(o,h,0,r.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);const A={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:o,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines};let _;if(y)A.extrudedHeight=h,A.offsetAttribute=e._offsetAttribute,_=function(e){const o=e.center,f=e.ellipsoid,h=e.semiMajorAxis;let y=i.Cartesian3.multiplyByScalar(f.geodeticSurfaceNormal(o,d),e.height,d);p.center=i.Cartesian3.add(o,y,p.center),p.radius=h,y=i.Cartesian3.multiplyByScalar(f.geodeticSurfaceNormal(o,y),e.extrudedHeight,y),m.center=i.Cartesian3.add(o,y,m.center),m.radius=h;let A=a.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions;const _=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:a.EllipseGeometryLibrary.raisePositionsToHeight(A,e,!0)})});A=_.position.values;const b=t.BoundingSphere.union(p,m);let g=A.length/3;if(n.defined(e.offsetAttribute)){let t=new Uint8Array(g);if(e.offsetAttribute===u.GeometryOffsetAttribute.TOP)t=t.fill(1,0,g/2);else{const i=e.offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;t=t.fill(i)}_.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}let x=n.defaultValue(e.numberOfVerticalLines,16);x=r.CesiumMath.clamp(x,0,g/2);const E=c.IndexDatatype.createTypedArray(g,2*g+2*x);g/=2;let M,C,G=0;for(M=0;M<g;++M)E[G++]=M,E[G++]=(M+1)%g,E[G++]=M+g,E[G++]=(M+1)%g+g;if(x>0){const e=Math.min(x,g);C=Math.round(g/e);const t=Math.min(C*x,g);for(M=0;M<t;M+=C)E[G++]=M,E[G++]=M+g}return{boundingSphere:b,attributes:_,indices:E}}(A);else if(_=function(e){const n=e.center;f=i.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(n,f),e.height,f),f=i.Cartesian3.add(n,f,f);const o=new t.BoundingSphere(f,e.semiMajorAxis),u=a.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,d=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:a.EllipseGeometryLibrary.raisePositionsToHeight(u,e,!1)})}),p=u.length/3,m=c.IndexDatatype.createTypedArray(p,2*p);let h=0;for(let e=0;e<p;++e)m[h++]=e,m[h++]=(e+1)%p;return{boundingSphere:o,attributes:d,indices:m}}(A),n.defined(e._offsetAttribute)){const t=_.attributes.position.values.length,i=e._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(t/3).fill(i);_.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new s.Geometry({attributes:_.attributes,indices:_.indices,primitiveType:s.PrimitiveType.LINES,boundingSphere:_.boundingSphere,offsetAttribute:e._offsetAttribute})},e.EllipseOutlineGeometry=h}));
