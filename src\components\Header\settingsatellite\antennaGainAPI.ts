import { basisBackendUrl } from '../../utils/api/basicURL';

// API基础URL - 根据实际后端地址调整
const API_BASE_URL = basisBackendUrl;

// API响应类型定义
interface ApiResponse {
  success: boolean;
  message?: string;
}

/**
 * 设置发射天线增益
 * @param gainTx 发射天线增益值
 * @returns Promise<boolean> 是否成功
 */
export async function setGainTx(gainTx: number): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_gain_tx`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        gain_tx: gainTx
      }),
    });

    const result: ApiResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('发射天线增益设置成功:', gainTx);
    return result.success;
  } catch (error) {
    console.error('设置发射天线增益失败:', error);
    return false;
  }
}

/**
 * 设置接收天线增益
 * @param gainRx 接收天线增益值
 * @returns Promise<boolean> 是否成功
 */
export async function setGainRx(gainRx: number): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_gain_rx`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        gain_rx: gainRx
      }),
    });

    const result: ApiResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('接收天线增益设置成功:', gainRx);
    return result.success;
  } catch (error) {
    console.error('设置接收天线增益失败:', error);
    return false;
  }
}

/**
 * 配置天线增益的主函数
 * @param gainTx 发射天线增益值
 * @param gainRx 接收天线增益值
 * @returns Promise<boolean> 是否成功
 */
export async function configureAntennaGain(gainTx: number, gainRx: number): Promise<boolean> {
  console.log('开始配置天线增益:', { gainTx, gainRx });
  
  try {
    // 设置发射天线增益
    const txSuccess = await setGainTx(gainTx);
    if (!txSuccess) {
      console.error('设置发射天线增益失败');
      return false;
    }

    // 设置接收天线增益
    const rxSuccess = await setGainRx(gainRx);
    if (!rxSuccess) {
      console.error('设置接收天线增益失败');
      return false;
    }

    console.log('天线增益配置完成:', { gainTx, gainRx });
    return true;

  } catch (error) {
    console.error('配置天线增益过程中发生异常:', error);
    return false;
  }
}
