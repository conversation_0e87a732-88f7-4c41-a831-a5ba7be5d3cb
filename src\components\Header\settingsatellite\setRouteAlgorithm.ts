import { notification } from 'antd';
import { basisBackendUrl, POST } from '../../utils/api/basicURL';

// 定义路由算法类型
type RouteAlgorithmKey = 'dijkstra' | 'floyd' | 'bfs';

// 算法中文名称映射
const ALGORITHM_NAME_MAPPING: Record<RouteAlgorithmKey, string> = {
  'dijkstra': 'Dijkstra (加权最短路径)',
  'floyd': 'Floyd-Warshall (所有对最短路径)',
  'bfs': '星地混合路由'
};

/**
 * 设置路由算法配置
 * @param algorithm 路由算法值（现在直接使用后端算法值，如 'dijkstra', 'floyd_warshall', 'bfs' 等）
 * @returns Promise<boolean> 设置是否成功
 */
export const setRouteAlgorithm = async (algorithm: string): Promise<boolean> => {
  try {
    // 验证算法值不为空
    if (!algorithm) {
      console.error('算法值不能为空');
      notification.error({
        message: '设置失败',
        description: '算法值不能为空'
      });
      return false;
    }

    console.log(`设置路由算法: ${algorithm}`);

    // 构建API URL
    const apiUrl = basisBackendUrl + '/set_path_compute_method';

    // 构建请求数据，按照新的API要求的格式
    const requestData = {
      path_algorithm: algorithm
    };

    // 调用后端API
    const response = await POST(apiUrl, requestData);

    console.log('路由算法设置成功:', response);
    notification.success({
      message: '设置成功',
      description: `路由算法已设置为: ${algorithm}`
    });
    return true;

  } catch (error) {
    console.error('设置路由算法异常:', error);
    notification.error({
      message: '设置失败',
      description: '网络异常，请检查网络连接'
    });
    return false;
  }
};

/**
 * 获取路由算法的中文名称
 * @param algorithm 路由算法值
 * @returns 算法的中文名称
 */
export const getRouteAlgorithmName = (algorithm: string): string => {
  if (algorithm in ALGORITHM_NAME_MAPPING) {
    return ALGORITHM_NAME_MAPPING[algorithm as RouteAlgorithmKey];
  }
  return '未知算法';
};

/**
 * 检查是否为有效的路由算法
 * @param algorithm 路由算法值
 * @returns 是否有效
 */
export const isValidRouteAlgorithm = (algorithm: string): boolean => {
  const validAlgorithms: RouteAlgorithmKey[] = ['dijkstra', 'floyd', 'bfs'];
  return validAlgorithms.includes(algorithm as RouteAlgorithmKey);
}; 