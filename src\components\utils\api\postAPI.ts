import { basisBackendUrl, POST } from "./basicURL";

// 请求和响应类型定义
export interface SetConstellationRequest {
  constellation_list: number[];
}

export interface GetPathCZMLRequest {
  terminalA_id: number;
  terminalB_id: number;
}

export interface GetPathCZMLResponse {
  czml_data: string;
}

export interface PostCurrentTimeRequest {
  current_time: number;
}

export interface AttachCommandRequest {
  terminal_id: number;
}

export interface AttachCommandResponse {
  cmd: string;
}

export interface GetCoreLogRequest {
  nf: string;
}

export interface GetCoreLogResponse {
  log: string;
}

export interface KvmNode {
  kvm_no: number;
  node_no: number;
  node_type: "gs" | "sat";
}

export interface SetParameterRequest {
  kvm_list: KvmNode[];
  mode: number;
  time: number;
  extend_nodes: number;
  start: number;
}

export interface SetParameterResponse {
  status: number;
  message: string;
}

// 选择卫星壳层
const setConstellation = basisBackendUrl + "/set_constellation";

/**
 * 设置选中的星座
 * @param constellation - 星座ID或星座ID数组
 * @returns Promise<string> - 响应消息
 */
async function setSelectedConstellation(constellation: number | number[]): Promise<string> {
  try {
    const requestData: SetConstellationRequest = {
      constellation_list: Array.isArray(constellation) ? constellation : [constellation]
    };
    
    const response = await POST<string>(setConstellation, requestData);
    return response;
  } catch (error) {
    console.error('Set constellation error:', error);
    throw error;
  }
}

// 选择卫星链路
const getPathCzml = basisBackendUrl + "/get_path";

/**
 * 获取终端路径 CZML 数据
 * @param source - 源终端ID
 * @param destination - 目标终端ID
 * @returns Promise<any> - 解析后的 CZML 数据
 */
async function getTerminalPathCzml(source: number, destination: number): Promise<any> {
  try {
    const requestData: GetPathCZMLRequest = { 
      terminalA_id: source,
      terminalB_id: destination
    };
    
    const response = await POST<string>(getPathCzml, requestData);
    // console.log(response);
    const czml = JSON.parse(response);
    return czml;
  } catch (error) {
    console.error('Get terminal path czml error:', error);
    throw error;
  }
}

// 获取当前时间
const currentTimeUrl = basisBackendUrl + "/current_time";

/**
 * 发送当前时间
 * @param currentTime - 当前时间（基于start time, 单位:ns）
 * @returns Promise<string> - 响应消息
 */
async function postCurrentTime(currentTime: number): Promise<string> {
  try {
    const requestData: PostCurrentTimeRequest = { 
      current_time: currentTime
    };
    
    // console.log('currentTime------',currentTime)
    const response = await POST<string>(currentTimeUrl, requestData);
    // console.log(response);
    return response;
  } catch (error) {
    console.error('Post current time error:', error);
    throw error;
  }
}

// 清除已选择的壳层
const clearChooseUrl = basisBackendUrl + "/clear_constellation";

/**
 * 清除选中的星座
 * @returns Promise<string> - 响应消息
 */
async function clearSelectedConstellation(): Promise<string> {
  try {
    const response = await POST<string>(clearChooseUrl);
    return response;
  } catch (error) {
    console.error('Clear constellation error:', error);
    throw error;
  }
}

// 附加命令API
const attachCommandAPI = basisBackendUrl + "/attach_command";

/**
 * 获取附加命令
 * @param terminalId - 终端ID
 * @returns Promise<string> - 命令字符串
 */
async function attachCommand(terminalId: number): Promise<string> {
  try {
      const requestData: AttachCommandRequest = { 
        terminal_id: terminalId
      };
      
      const response = await POST<AttachCommandResponse>(attachCommandAPI, requestData);
      return response.cmd;
  } catch (error) {
    console.error('Get attach cmd error:', error);
    throw error;
  }
}

// 获取核心网日志
const getCoreLogUrl = "http://10.176.26.78:5000/api/get_log_core";

/**
 * 获取核心网日志
 * @param nf - 核心网网元名称 (如: "smf", "amf", "upf" 等)
 * @returns Promise<string> - 日志内容
 */
async function getCoreLog(nf: string): Promise<string> {
  try {
    const requestData: GetCoreLogRequest = { 
      nf: nf
    };
    
    const response = await POST<GetCoreLogResponse>(getCoreLogUrl, requestData);
    // console.log('Get core log response:', response);
    return response.log;
  } catch (error) {
    console.error('Get core log error:', error);
    throw error;
  }
}

// 设置模拟参数
const parameterSetUrl = "http://10.176.26.78:5000/api/parameter_set";

/**
 * 设置模拟参数
 * @param kvmList - 需要选择的节点列表
 * @param mode - 模拟模式（1.透明 2.再生 3.再生+部分核心网上星 4.再生+全部核心网上星）
 * @param time - 模拟的开始运行时间（以ns为单位）
 * @param extendNodes - 需要额外选择的节点数量，不能大于10，默认为0
 * @param start - 是否启动模拟，0表示不启动，1表示启动，默认为0
 * @returns Promise<SetParameterResponse> - 设置结果
 */
async function setSimulationParameters(
  kvmList: KvmNode[], 
  mode: number, 
  time: number, 
  extendNodes: number = 0, 
  start: number = 0
): Promise<SetParameterResponse> {
  try {
    const requestData: SetParameterRequest = { 
      kvm_list: kvmList,
      mode: mode,
      time: time,
      extend_nodes: extendNodes,
      start: start
    };
    
    const response = await POST<SetParameterResponse>(parameterSetUrl, requestData);
    console.log('Set simulation parameters response:', response);
    return response;
  } catch (error) {
    console.error('Set simulation parameters error:', error);
    throw error;
  }
}

// 导出所有获取数据的异步函数
export { 
  setSelectedConstellation, 
  getTerminalPathCzml, 
  postCurrentTime, 
  clearSelectedConstellation, 
  attachCommand, 
  getCoreLog, 
  setSimulationParameters
};
