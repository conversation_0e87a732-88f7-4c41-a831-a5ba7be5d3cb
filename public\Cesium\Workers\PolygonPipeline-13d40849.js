/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Matrix2-21f90abf","./RuntimeError-cef79f54","./ComponentDatatype-4028c72d","./defaultValue-4607806f","./EllipsoidRhumbLine-bf1c0ab0","./GeometryAttribute-3c090c07","./WebGLConstants-f100e3dd"],(function(e,t,n,r,a,i,o,u){"use strict";var s=p,x=p;function p(e,t,n){n=n||2;var r,a,i,o,u,s,x,p=t&&t.length,h=p?t[0]*n:e.length,f=l(e,0,h,n,!0),c=[];if(!f||f.next===f.prev)return c;if(p&&(f=function(e,t,n,r){var a,i,o,u=[];for(a=0,i=t.length;a<i;a++)(o=l(e,t[a]*r,a<i-1?t[a+1]*r:e.length,r,!1))===o.next&&(o.steiner=!0),u.push(b(o));for(u.sort(m),a=0;a<u.length;a++)n=C(u[a],n);return n}(e,t,f,n)),e.length>80*n){r=i=e[0],a=o=e[1];for(var v=n;v<h;v+=n)(u=e[v])<r&&(r=u),(s=e[v+1])<a&&(a=s),u>i&&(i=u),s>o&&(o=s);x=0!==(x=Math.max(i-r,o-a))?32767/x:0}return y(f,c,n,r,a,x,0),c}function l(e,t,n,r,a){var i,o;if(a===B(e,t,n,r)>0)for(i=t;i<n;i+=r)o=G(i,e[i],e[i+1],o);else for(i=n-r;i>=t;i-=r)o=G(i,e[i],e[i+1],o);return o&&M(o,o.next)&&(O(o),o=o.next),o}function h(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!M(r,r.next)&&0!==E(r.prev,r,r.next))r=r.next;else{if(O(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function y(e,t,n,r,a,i,o){if(e){!o&&i&&function(e,t,n,r){var a=e;do{0===a.z&&(a.z=w(a.x,a.y,t,n,r)),a.prevZ=a.prev,a.nextZ=a.next,a=a.next}while(a!==e);a.prevZ.nextZ=null,a.prevZ=null,function(e){var t,n,r,a,i,o,u,s,x=1;do{for(n=e,e=null,i=null,o=0;n;){for(o++,r=n,u=0,t=0;t<x&&(u++,r=r.nextZ);t++);for(s=x;u>0||s>0&&r;)0!==u&&(0===s||!r||n.z<=r.z)?(a=n,n=n.nextZ,u--):(a=r,r=r.nextZ,s--),i?i.nextZ=a:e=a,a.prevZ=i,i=a;n=r}i.nextZ=null,x*=2}while(o>1)}(a)}(e,r,a,i);for(var u,s,x=e;e.prev!==e.next;)if(u=e.prev,s=e.next,i?c(e,r,a,i):f(e))t.push(u.i/n|0),t.push(e.i/n|0),t.push(s.i/n|0),O(e),e=s.next,x=s.next;else if((e=s)===x){o?1===o?y(e=v(h(e),t,n),t,n,r,a,i,2):2===o&&d(e,t,n,r,a,i):y(h(e),t,n,r,a,i,1);break}}}function f(e){var t=e.prev,n=e,r=e.next;if(E(t,n,r)>=0)return!1;for(var a=t.x,i=n.x,o=r.x,u=t.y,s=n.y,x=r.y,p=a<i?a<o?a:o:i<o?i:o,l=u<s?u<x?u:x:s<x?s:x,h=a>i?a>o?a:o:i>o?i:o,y=u>s?u>x?u:x:s>x?s:x,f=r.next;f!==t;){if(f.x>=p&&f.x<=h&&f.y>=l&&f.y<=y&&A(a,u,i,s,o,x,f.x,f.y)&&E(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}function c(e,t,n,r){var a=e.prev,i=e,o=e.next;if(E(a,i,o)>=0)return!1;for(var u=a.x,s=i.x,x=o.x,p=a.y,l=i.y,h=o.y,y=u<s?u<x?u:x:s<x?s:x,f=p<l?p<h?p:h:l<h?l:h,c=u>s?u>x?u:x:s>x?s:x,v=p>l?p>h?p:h:l>h?l:h,d=w(y,f,t,n,r),m=w(c,v,t,n,r),C=e.prevZ,g=e.nextZ;C&&C.z>=d&&g&&g.z<=m;){if(C.x>=y&&C.x<=c&&C.y>=f&&C.y<=v&&C!==a&&C!==o&&A(u,p,s,l,x,h,C.x,C.y)&&E(C.prev,C,C.next)>=0)return!1;if(C=C.prevZ,g.x>=y&&g.x<=c&&g.y>=f&&g.y<=v&&g!==a&&g!==o&&A(u,p,s,l,x,h,g.x,g.y)&&E(g.prev,g,g.next)>=0)return!1;g=g.nextZ}for(;C&&C.z>=d;){if(C.x>=y&&C.x<=c&&C.y>=f&&C.y<=v&&C!==a&&C!==o&&A(u,p,s,l,x,h,C.x,C.y)&&E(C.prev,C,C.next)>=0)return!1;C=C.prevZ}for(;g&&g.z<=m;){if(g.x>=y&&g.x<=c&&g.y>=f&&g.y<=v&&g!==a&&g!==o&&A(u,p,s,l,x,h,g.x,g.y)&&E(g.prev,g,g.next)>=0)return!1;g=g.nextZ}return!0}function v(e,t,n){var r=e;do{var a=r.prev,i=r.next.next;!M(a,i)&&Z(a,r,r.next,i)&&R(a,i)&&R(i,a)&&(t.push(a.i/n|0),t.push(r.i/n|0),t.push(i.i/n|0),O(r),O(r.next),r=e=i),r=r.next}while(r!==e);return h(r)}function d(e,t,n,r,a,i){var o=e;do{for(var u=o.next.next;u!==o.prev;){if(o.i!==u.i&&S(o,u)){var s=D(o,u);return o=h(o,o.next),s=h(s,s.next),y(o,t,n,r,a,i,0),void y(s,t,n,r,a,i,0)}u=u.next}o=o.next}while(o!==e)}function m(e,t){return e.x-t.x}function C(e,t){var n=function(e,t){var n,r=t,a=e.x,i=e.y,o=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var u=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(u<=a&&u>o&&(o=u,n=r.x<r.next.x?r:r.next,u===a))return n}r=r.next}while(r!==t);if(!n)return null;var s,x=n,p=n.x,l=n.y,h=1/0;r=n;do{a>=r.x&&r.x>=p&&a!==r.x&&A(i<l?a:o,i,p,l,i<l?o:a,i,r.x,r.y)&&(s=Math.abs(i-r.y)/(a-r.x),R(r,e)&&(s<h||s===h&&(r.x>n.x||r.x===n.x&&g(n,r)))&&(n=r,h=s)),r=r.next}while(r!==x);return n}(e,t);if(!n)return t;var r=D(n,e);return h(r,r.next),h(n,n.next)}function g(e,t){return E(e.prev,e,t.prev)<0&&E(t.next,e,e.next)<0}function w(e,t,n,r,a){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*a|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*a|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function b(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function A(e,t,n,r,a,i,o,u){return(a-o)*(t-u)>=(e-o)*(i-u)&&(e-o)*(r-u)>=(n-o)*(t-u)&&(n-o)*(i-u)>=(a-o)*(r-u)}function S(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&Z(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(R(e,t)&&R(t,e)&&function(e,t){var n=e,r=!1,a=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&a<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(E(e.prev,e,t.prev)||E(e,t.prev,t))||M(e,t)&&E(e.prev,e,e.next)>0&&E(t.prev,t,t.next)>0)}function E(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function M(e,t){return e.x===t.x&&e.y===t.y}function Z(e,t,n,r){var a=L(E(e,t,n)),i=L(E(e,t,r)),o=L(E(n,r,e)),u=L(E(n,r,t));return a!==i&&o!==u||(!(0!==a||!z(e,n,t))||(!(0!==i||!z(e,r,t))||(!(0!==o||!z(n,e,r))||!(0!==u||!z(n,t,r)))))}function z(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function L(e){return e>0?1:e<0?-1:0}function R(e,t){return E(e.prev,e,e.next)<0?E(e,t,e.next)>=0&&E(e,e.prev,t)>=0:E(e,t,e.prev)<0||E(e,e.next,t)<0}function D(e,t){var n=new T(e.i,e.x,e.y),r=new T(t.i,t.x,t.y),a=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=a,a.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function G(e,t,n,r){var a=new T(e,t,n);return r?(a.next=r.next,a.prev=r,r.next.prev=a,r.next=a):(a.prev=a,a.next=a),a}function O(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function T(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function B(e,t,n,r){for(var a=0,i=t,o=n-r;i<n;i+=r)a+=(e[o]-e[i])*(e[i+1]+e[o+1]),o=i;return a}p.deviation=function(e,t,n,r){var a=t&&t.length,i=a?t[0]*n:e.length,o=Math.abs(B(e,0,i,n));if(a)for(var u=0,s=t.length;u<s;u++){var x=t[u]*n,p=u<s-1?t[u+1]*n:e.length;o-=Math.abs(B(e,x,p,n))}var l=0;for(u=0;u<r.length;u+=3){var h=r[u]*n,y=r[u+1]*n,f=r[u+2]*n;l+=Math.abs((e[h]-e[f])*(e[y+1]-e[h+1])-(e[h]-e[y])*(e[f+1]-e[h+1]))}return 0===o&&0===l?0:Math.abs((l-o)/o)},p.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,a=0;a<e.length;a++){for(var i=0;i<e[a].length;i++)for(var o=0;o<t;o++)n.vertices.push(e[a][i][o]);a>0&&(r+=e[a-1].length,n.holes.push(r))}return n},s.default=x;const W={CLOCKWISE:u.WebGLConstants.CW,COUNTER_CLOCKWISE:u.WebGLConstants.CCW,validate:function(e){return e===W.CLOCKWISE||e===W.COUNTER_CLOCKWISE}};var P=Object.freeze(W);const $=new t.Cartesian3,I=new t.Cartesian3,N={computeArea2D:function(e){const t=e.length;let n=0;for(let r=t-1,a=0;a<t;r=a++){const t=e[r],i=e[a];n+=t.x*i.y-i.x*t.y}return.5*n},computeWindingOrder2D:function(e){return N.computeArea2D(e)>0?P.COUNTER_CLOCKWISE:P.CLOCKWISE},triangulate:function(e,n){const r=t.Cartesian2.packArray(e);return s(r,n,2)}},U=new t.Cartesian3,_=new t.Cartesian3,K=new t.Cartesian3,V=new t.Cartesian3,F=new t.Cartesian3,k=new t.Cartesian3,q=new t.Cartesian3,j=new t.Cartesian2,H=new t.Cartesian2,J=new t.Cartesian2,Q=new t.Cartesian2;N.computeSubdivision=function(e,n,i,u,s){s=a.defaultValue(s,r.CesiumMath.RADIANS_PER_DEGREE);const x=a.defined(u),p=i.slice(0);let l;const h=n.length,y=new Array(3*h),f=new Array(2*h);let c=0,v=0;for(l=0;l<h;l++){const e=n[l];if(y[c++]=e.x,y[c++]=e.y,y[c++]=e.z,x){const e=u[l];f[v++]=e.x,f[v++]=e.y}}const d=[],m={},C=e.maximumRadius,g=r.CesiumMath.chordLength(s,C),w=g*g;for(;p.length>0;){const e=p.pop(),n=p.pop(),r=p.pop(),i=t.Cartesian3.fromArray(y,3*r,U),o=t.Cartesian3.fromArray(y,3*n,_),u=t.Cartesian3.fromArray(y,3*e,K);let s,h,c;x&&(s=t.Cartesian2.fromArray(f,2*r,j),h=t.Cartesian2.fromArray(f,2*n,H),c=t.Cartesian2.fromArray(f,2*e,J));const v=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(i,V),C,V),g=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(o,F),C,F),b=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(u,k),C,k),A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(v,g,q)),S=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(g,b,q)),E=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(b,v,q)),M=Math.max(A,S,E);let Z,z,L;M>w?A===M?(Z=`${Math.min(r,n)} ${Math.max(r,n)}`,l=m[Z],a.defined(l)||(z=t.Cartesian3.add(i,o,q),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(s,h,Q),t.Cartesian2.multiplyByScalar(L,.5,L),f.push(L.x,L.y))),p.push(r,l,e),p.push(l,n,e)):S===M?(Z=`${Math.min(n,e)} ${Math.max(n,e)}`,l=m[Z],a.defined(l)||(z=t.Cartesian3.add(o,u,q),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(h,c,Q),t.Cartesian2.multiplyByScalar(L,.5,L),f.push(L.x,L.y))),p.push(n,l,r),p.push(l,e,r)):E===M&&(Z=`${Math.min(e,r)} ${Math.max(e,r)}`,l=m[Z],a.defined(l)||(z=t.Cartesian3.add(u,i,q),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(c,s,Q),t.Cartesian2.multiplyByScalar(L,.5,L),f.push(L.x,L.y))),p.push(e,l,n),p.push(l,r,n)):(d.push(r),d.push(n),d.push(e))}const b={attributes:{position:new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y})},indices:d,primitiveType:o.PrimitiveType.TRIANGLES};return x&&(b.attributes.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:f})),new o.Geometry(b)};const X=new t.Cartographic,Y=new t.Cartographic,ee=new t.Cartographic,te=new t.Cartographic;N.computeRhumbLineSubdivision=function(e,n,u,s,x){x=a.defaultValue(x,r.CesiumMath.RADIANS_PER_DEGREE);const p=a.defined(s),l=u.slice(0);let h;const y=n.length,f=new Array(3*y),c=new Array(2*y);let v=0,d=0;for(h=0;h<y;h++){const e=n[h];if(f[v++]=e.x,f[v++]=e.y,f[v++]=e.z,p){const e=s[h];c[d++]=e.x,c[d++]=e.y}}const m=[],C={},g=e.maximumRadius,w=r.CesiumMath.chordLength(x,g),b=new i.EllipsoidRhumbLine(void 0,void 0,e),A=new i.EllipsoidRhumbLine(void 0,void 0,e),S=new i.EllipsoidRhumbLine(void 0,void 0,e);for(;l.length>0;){const n=l.pop(),r=l.pop(),i=l.pop(),o=t.Cartesian3.fromArray(f,3*i,U),u=t.Cartesian3.fromArray(f,3*r,_),s=t.Cartesian3.fromArray(f,3*n,K);let x,y,v;p&&(x=t.Cartesian2.fromArray(c,2*i,j),y=t.Cartesian2.fromArray(c,2*r,H),v=t.Cartesian2.fromArray(c,2*n,J));const d=e.cartesianToCartographic(o,X),g=e.cartesianToCartographic(u,Y),E=e.cartesianToCartographic(s,ee);b.setEndPoints(d,g);const M=b.surfaceDistance;A.setEndPoints(g,E);const Z=A.surfaceDistance;S.setEndPoints(E,d);const z=S.surfaceDistance,L=Math.max(M,Z,z);let R,D,G,O,T;L>w?M===L?(R=`${Math.min(i,r)} ${Math.max(i,r)}`,h=C[R],a.defined(h)||(D=b.interpolateUsingFraction(.5,te),G=.5*(d.height+g.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),f.push(O.x,O.y,O.z),h=f.length/3-1,C[R]=h,p&&(T=t.Cartesian2.add(x,y,Q),t.Cartesian2.multiplyByScalar(T,.5,T),c.push(T.x,T.y))),l.push(i,h,n),l.push(h,r,n)):Z===L?(R=`${Math.min(r,n)} ${Math.max(r,n)}`,h=C[R],a.defined(h)||(D=A.interpolateUsingFraction(.5,te),G=.5*(g.height+E.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),f.push(O.x,O.y,O.z),h=f.length/3-1,C[R]=h,p&&(T=t.Cartesian2.add(y,v,Q),t.Cartesian2.multiplyByScalar(T,.5,T),c.push(T.x,T.y))),l.push(r,h,i),l.push(h,n,i)):z===L&&(R=`${Math.min(n,i)} ${Math.max(n,i)}`,h=C[R],a.defined(h)||(D=S.interpolateUsingFraction(.5,te),G=.5*(E.height+d.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),f.push(O.x,O.y,O.z),h=f.length/3-1,C[R]=h,p&&(T=t.Cartesian2.add(v,x,Q),t.Cartesian2.multiplyByScalar(T,.5,T),c.push(T.x,T.y))),l.push(n,h,r),l.push(h,i,r)):(m.push(i),m.push(r),m.push(n))}const E={attributes:{position:new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f})},indices:m,primitiveType:o.PrimitiveType.TRIANGLES};return p&&(E.attributes.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:c})),new o.Geometry(E)},N.scaleToGeodeticHeight=function(e,n,r,i){r=a.defaultValue(r,t.Ellipsoid.WGS84);let o=$,u=I;if(n=a.defaultValue(n,0),i=a.defaultValue(i,!0),a.defined(e)){const a=e.length;for(let s=0;s<a;s+=3)t.Cartesian3.fromArray(e,s,u),i&&(u=r.scaleToGeodeticSurface(u,u)),0!==n&&(o=r.geodeticSurfaceNormal(u,o),t.Cartesian3.multiplyByScalar(o,n,o),t.Cartesian3.add(u,o,u)),e[s]=u.x,e[s+1]=u.y,e[s+2]=u.z}return e},e.PolygonPipeline=N,e.WindingOrder=P}));
