import { basisBackendUrl, POST } from '../../utils/api/basicURL';
/**
 * 可见卫星响应接口
 */
export interface VisibleSatelliteResponse {
  success: boolean;
  data?: {
    gs_id: number;
    time_ns: number;
    visible_satellites: string[];
    satellite_count: number;
  };
  message?: string;
  error?: string;
}

/**
 * 获取地面站可见卫星
 * @param gs_id 地面站ID
 * @param time_ns 时间戳，单位纳秒
 * @returns Promise<VisibleSatelliteResponse>
 */
export async function getVisibleSatellites(gs_id: number, time_ns: number): Promise<VisibleSatelliteResponse> {
  try {
    // console.log('调用可见卫星接口，参数:', { gs_id, time_ns });
    
    const response = await fetch(`${basisBackendUrl}/get_visible_satellite`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify({
        gs_id: gs_id,
        time_ns: time_ns
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status === 200 && data.data) {
      // console.log('可见卫星数据获取成功:', data.data);
      return {
        success: true,
        data: data.data
      };
    } else {
      console.warn('可见卫星接口返回错误:', data.message);
      return {
        success: false,
        message: data.message || '获取可见卫星失败',
        data: {
          gs_id: gs_id,
          time_ns: time_ns,
          visible_satellites: [],
          satellite_count: 0
        }
      };
    }
  } catch (error) {
    console.error('获取可见卫星时出错:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      data: {
        gs_id: gs_id,
        time_ns: time_ns,
        visible_satellites: [],
        satellite_count: 0
      }
    };
  }
}

/**
 * 获取地面站可见卫星的简化版本
 * 直接返回可见卫星列表，失败时返回空数组
 * @param gs_id 地面站ID
 * @param time_ns 时间戳，单位纳秒
 * @returns Promise<string[]>
 */
export async function getVisibleSatellitesList(gs_id: number, time_ns: number): Promise<string[]> {
  try {
    const result = await getVisibleSatellites(gs_id, time_ns);
    return result.data?.visible_satellites || [];
  } catch (error) {
    console.error('获取可见卫星列表失败:', error);
    return [];
  }
}
