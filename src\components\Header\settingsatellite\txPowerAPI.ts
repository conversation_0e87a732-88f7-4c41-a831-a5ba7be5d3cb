import { basisBackendUrl } from '../../utils/api/basicURL';

// API基础URL - 根据实际后端地址调整
const API_BASE_URL = basisBackendUrl;

// API响应类型定义
interface ApiResponse {
  success: boolean;
  message?: string;
}

/**
 * 设置发射功率
 * @param txPower 发射功率值
 * @returns Promise<boolean> 是否成功
 */
export async function setTxPower(txPower: number): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_tx_power`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tx_power: txPower
      }),
    });

    const result: ApiResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('发射功率设置成功:', txPower);
    return result.success;
  } catch (error) {
    console.error('设置发射功率失败:', error);
    return false;
  }
}

/**
 * 配置发射功率的主函数
 * @param txPower 发射功率值
 * @returns Promise<boolean> 是否成功
 */
export async function configureTxPower(txPower: number): Promise<boolean> {
  console.log('开始配置发射功率:', txPower);
  
  try {
    const success = await setTxPower(txPower);
    if (!success) {
      console.error('设置发射功率失败');
      return false;
    }

    console.log('发射功率配置完成:', txPower);
    return true;

  } catch (error) {
    console.error('配置发射功率过程中发生异常:', error);
    return false;
  }
}
