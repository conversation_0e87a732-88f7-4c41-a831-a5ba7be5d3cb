import {
  calculateBeamOrientation,
  generateHexagonalCellCenters,
  calculateDistance,
  findNearestCell,
  createHexagonCesiumVertices,
  CellCenter,
  BeamInfo,
  calculateGroundProjection,
  calculateBeamGroundProjection
} from './Utils';
import useSimulationStore from '../../../store/simulationStore';

declare const Cesium: any;

// 整合管理器参数接口
export interface BeamCellManagerParams {
  viewer: any;
}

// 整合的波束小区管理器类
class BeamCellManager {
  private viewer: any;
  private beamEntities: any[] = [];
  private selectedSatelliteId: number | null = null;
  private position: any;
  private beamNum: number = 1;
  private beamRadius: number = 0;

  // 小区相关属性
  private cellPrimitive: any[] | any = null;
  private cellLength: number = 0;
  private cellCenters: CellCenter[] = [];
  private currentCenterCellIndex: number = 0;
  private cellLayers: number = 0;
  private beamPositions: BeamInfo[] = []; // 存储所有波束的位置信息

  constructor(params: BeamCellManagerParams) {
    this.viewer = params.viewer;
  }

  private calculateBeamParameters(storeBeamwidth?: number): number {
    const currentTime = this.viewer.clock.currentTime;
    const satellitePosition = this.position.getValue(currentTime);
    const cartographic = Cesium.Cartographic.fromCartesian(satellitePosition);
    const height = Math.abs(cartographic.height);
    const earthRadius = 6371393;
    const earthHeight = (earthRadius * earthRadius) / (height + earthRadius);
    const adjustedEarthHeight = earthHeight + ((earthRadius - earthHeight) * 8) / 9;
    let bottomRadius = Math.sqrt(earthRadius * earthRadius - adjustedEarthHeight * adjustedEarthHeight) / 20;
    this.beamRadius = bottomRadius + (storeBeamwidth ? storeBeamwidth * 4000 : 0);
    const satelliteLength = Math.abs(height + earthRadius - adjustedEarthHeight);

    return satelliteLength;
  }

  private initializeCells(): void {
    if (!this.position) return;

    // 获取当前卫星投影点
    const currentTime = this.viewer.clock.currentTime;
    const satellitePosition = this.position.getValue(currentTime);
    if (!satellitePosition) return;

    const projection = calculateGroundProjection(satellitePosition);

    // 生成初始小区中心点
    this.cellCenters = generateHexagonalCellCenters(
      projection.longitude,
      projection.latitude,
      this.cellLength,
      this.cellLayers
    );

    this.currentCenterCellIndex = 0; // 初始中心小区为第一个

    // 创建并显示小区
    this.createAndDisplayCells();
  }

  private createAndDisplayCells(): void {
    // 清除之前的小区图元
    this.clearCells();

    if (this.cellCenters.length === 0) return;

    // 更新波束位置信息
    this.updateBeamPositions();

    // 创建几何体实例数组
    const geometryInstances: any[] = [];

    this.cellCenters.forEach((cellCenter, index) => {
      // 判断小区是否被任何波束覆盖
      const isHighlighted = this.isCellCoveredByBeams(cellCenter);

      // 创建六边形顶点
      const vertices = createHexagonCesiumVertices(
        cellCenter.longitude,
        cellCenter.latitude,
        this.cellLength
      );

      // 1. 创建六边形边框（所有小区都有浅蓝色边框）
      // 使用 PolylineGeometry 来创建可控制线宽的边框
      const positions = [...vertices, vertices[0]]; // 闭合多边形
      const polylineGeometry = new Cesium.PolylineGeometry({
        positions: positions,
        width: 1.5,
        vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
        clampToGround: false
      });

      const outlineInstance = new Cesium.GeometryInstance({
        geometry: polylineGeometry,
        id: `cell_outline_${index}`,
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(
            Cesium.Color.ORANGE.withAlpha(0.8)
          )
        }
      });

      geometryInstances.push(outlineInstance);

      // 2. 如果是高亮小区，创建橙色填充
      if (isHighlighted) {
        const fillGeometry = new Cesium.PolygonGeometry({
          polygonHierarchy: new Cesium.PolygonHierarchy(vertices),
          height: 0
        });

        const fillInstance = new Cesium.GeometryInstance({
          geometry: fillGeometry,
          id: `cell_fill_${index}`,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(
              Cesium.Color.DARKORANGE.withAlpha(0.8)
            )
          }
        });

        geometryInstances.push(fillInstance);
      }
    });

    // 分离边框和填充几何实例
    const outlineInstances = geometryInstances.filter(instance =>
      instance.id.includes('outline')
    );
    const fillInstances = geometryInstances.filter(instance =>
      instance.id.includes('fill')
    );

    // 创建边框图元
    if (outlineInstances.length > 0) {
      const outlinePrimitive = new Cesium.Primitive({
        geometryInstances: outlineInstances,
        appearance: new Cesium.PolylineColorAppearance({
          translucent: false, // 设为不透明，确保边框清晰可见
          renderState: {
            depthTest: {
              enabled: true
            },
            depthMask: true
          }
        }),
        asynchronous: false
      });

      this.viewer.scene.primitives.add(outlinePrimitive);

      // 存储图元引用（用于清理）
      if (!this.cellPrimitive) {
        this.cellPrimitive = [];
      }
      this.cellPrimitive.push(outlinePrimitive);
    }

    // 创建填充图元
    if (fillInstances.length > 0) {
      const fillPrimitive = new Cesium.Primitive({
        geometryInstances: fillInstances,
        appearance: new Cesium.PerInstanceColorAppearance({
          translucent: true,
          closed: false
        }),
        asynchronous: false
      });

      this.viewer.scene.primitives.add(fillPrimitive);

      // 存储图元引用（用于清理）
      if (!this.cellPrimitive) {
        this.cellPrimitive = [];
      }
      this.cellPrimitive.push(fillPrimitive);
    }
  }

  private clearCells(): void {
    if (this.cellPrimitive) {
      if (Array.isArray(this.cellPrimitive)) {
        // 如果是数组，清除所有图元
        this.cellPrimitive.forEach(primitive => {
          if (primitive) {
            this.viewer.scene.primitives.remove(primitive);
          }
        });
        this.cellPrimitive = [];
      } else {
        // 兼容旧的单个图元格式
        this.viewer.scene.primitives.remove(this.cellPrimitive);
        this.cellPrimitive = null;
      }
    }
  }

  private updateBeamPositions(): void {
    this.beamPositions = [];

    if (!this.position || !this.beamEntities) return;

    const currentTime = this.viewer.clock.currentTime;
    const satellitePosition = this.position.getValue(currentTime);
    if (!satellitePosition) return;

    // 直接从波束实体获取实际的地面投影点
    this.beamEntities.forEach((beamEntity) => {
      if (beamEntity && beamEntity.position) {
        // 获取波束圆锥的中心位置
        const beamCenterPosition = beamEntity.position.getValue(currentTime);
        if (beamCenterPosition) {
          // 计算波束圆锥底部在地面的投影点
          // 波束圆锥指向地面，所以我们需要计算圆锥底部中心的地面投影
          const beamGroundProjection = calculateBeamGroundProjection(
            satellitePosition,
            beamCenterPosition,
            this.viewer
          );

          if (beamGroundProjection) {
            this.beamPositions.push({
              position: beamGroundProjection,
              radius: this.beamRadius
            });
          }
        }
      }
    });
  }

  private isCellCoveredByBeams(cellCenter: CellCenter): boolean {
    return this.beamPositions.some(beam => {
      const distance = calculateDistance(
        cellCenter.longitude,
        cellCenter.latitude,
        beam.position.longitude,
        beam.position.latitude
      );
      return distance <= beam.radius;
    });
  }

  public updateCells(): void {
    if (!this.position || this.selectedSatelliteId === null || this.cellCenters.length === 0) return;

    const satelliteIds = useSimulationStore.getState().getSatelliteIds();

    const shouldClearBeamsAndCells = satelliteIds.length > 0 &&
      satelliteIds.includes(this.selectedSatelliteId);

    if (shouldClearBeamsAndCells) {
      this.clearAll();
      return;
    }

    const currentTime = this.viewer.clock.currentTime;
    const satellitePosition = this.position.getValue(currentTime);
    if (!satellitePosition) return;

    // 计算当前卫星投影点
    const projection = calculateGroundProjection(satellitePosition);

    // 检查是否需要切换中心小区
    const [nearestCellIndex, nearestCellDistance] = findNearestCell(
      projection.longitude,
      projection.latitude,
      this.cellCenters
    );

    // 如果最近的小区不是当前中心小区，则重新生成小区簇
    if (nearestCellIndex !== this.currentCenterCellIndex) {
      if (nearestCellDistance < this.cellLength) {
        this.currentCenterCellIndex = nearestCellIndex;
        const newCenterCell = this.cellCenters[nearestCellIndex];

        this.cellCenters = generateHexagonalCellCenters(
          newCenterCell.longitude,
          newCenterCell.latitude,
          this.cellLength,
          this.cellLayers
        );
      }
      else {
        this.cellCenters = generateHexagonalCellCenters(
          projection.longitude,
          projection.latitude,
          this.cellLength,
          this.cellLayers
        );
      }
      this.currentCenterCellIndex = 0;
    }

    // 重新创建并显示所有小区
    this.createAndDisplayCells();
  }

  public createBeamsAndCells(
    position: any,
    satelliteId: number
  ): boolean {
    try {
      // 检查是否应该创建波束和小区
      const satelliteIds = useSimulationStore.getState().getSatelliteIds();

      const shouldClearBeamsAndCells = satelliteIds.length > 0 && this.selectedSatelliteId != null &&
        satelliteIds.includes(this.selectedSatelliteId);

      if (shouldClearBeamsAndCells) {
        return true; // 返回成功，但不创建波束和小区
      }

      this.position = position;
      this.selectedSatelliteId = satelliteId;
      // 从store获取天线参数
      const { antennaParameters } = useSimulationStore.getState();
      this.beamNum = antennaParameters.beam_num || 1;
      // 内部计算波束参数
      const satelliteLength = this.calculateBeamParameters(antennaParameters.beamwidth);

      // 计算小区参数
      this.cellLength = this.beamRadius / Math.sqrt(3);
      // this.cellLength = this.beamRadius / 10;

      if (this.beamNum > 7)
        this.cellLayers = 5;
      else if (this.beamNum > 1)
        this.cellLayers = 3;
      else
        this.cellLayers = 2;

      // 创建波束
      this.beamEntities = this.createBeams(satelliteLength);

      // 初始化小区
      this.initializeCells();

      return true;
    } catch (error) {
      console.error(`创建波束和小区失败: ${error}`);
      return false;
    }
  }

  private createBeams(
    height: number
  ): any[] {
    // 使用固定的明亮颜色
    let beamColor = Cesium.Color.fromCssColorString('#ff7834');

    // 波束最大层数
    const beamLayerNum = 4;
    const entities: any[] = [];
    const beamOpacity = 0.2; // 更透明的设置

    const cellCenters: {dx: number, dy: number}[] = [{dx: 0, dy: 0}];

    for (let layer = 1; layer <= beamLayerNum && cellCenters.length < this.beamNum; layer++){
      let x = 0, y = -layer * Math.sqrt(3) * this.beamRadius;
      for(let angle = 30; angle <= 330 && cellCenters.length < this.beamNum; angle += 60){
        for (let step = 0; step < layer && cellCenters.length < this.beamNum; step++){
          cellCenters.push({dx: x, dy: y});
          const rad = angle / 180 * Math.PI;
          x += Math.sqrt(3) * Math.cos(rad) * this.beamRadius;
          y += Math.sqrt(3) * Math.sin(rad) * this.beamRadius;
        }
      }
    }

    cellCenters.forEach((cell, idx) => {
      const radarId = `radarScan_cell_${idx}`;

      // 计算圆锥中心位置
      const cylinderPosition = new Cesium.CallbackProperty((time: any) => {
        const satellitePosition = this.position.getValue(time);
        if (!satellitePosition) return undefined;

        const earthCenter = new Cesium.Cartesian3(0, 0, 0);
        const satToEarth = Cesium.Cartesian3.subtract(
          earthCenter,
          satellitePosition,
          new Cesium.Cartesian3()
        );
        Cesium.Cartesian3.normalize(satToEarth, satToEarth);

        // 构造垂直于satToEarth的两个正交向量
        let ref = Math.abs(satToEarth.x) < 0.9 ? Cesium.Cartesian3.UNIT_X : Cesium.Cartesian3.UNIT_Y;
        let ortho1 = Cesium.Cartesian3.cross(satToEarth, ref, new Cesium.Cartesian3());
        Cesium.Cartesian3.normalize(ortho1, ortho1);
        let ortho2 = Cesium.Cartesian3.cross(satToEarth, ortho1, new Cesium.Cartesian3());
        Cesium.Cartesian3.normalize(ortho2, ortho2);

        // 圆锥中心位置
        const midpoint = Cesium.Cartesian3.add(
        satellitePosition,
        Cesium.Cartesian3.multiplyByScalar(
          satToEarth,
          height / 2,
          new Cesium.Cartesian3()
        ),
        new Cesium.Cartesian3()
      );

        return Cesium.Cartesian3.add(
        midpoint,
        Cesium.Cartesian3.add(
          Cesium.Cartesian3.multiplyByScalar(ortho1,cell.dx / 2,new Cesium.Cartesian3()),
          Cesium.Cartesian3.multiplyByScalar(ortho2,cell.dy / 2,new Cesium.Cartesian3()),
          new Cesium.Cartesian3()
        ),
        new Cesium.Cartesian3()
      );
      }, false);

      // 计算圆锥方向
      const cylinderOrientation = new Cesium.CallbackProperty((time: any) => {
        const satellitePosition = this.position.getValue(time);
        if (!satellitePosition) return undefined;

        const satToCylinder = Cesium.Cartesian3.subtract(
        cylinderPosition.getValue(time),
        satellitePosition,
        new Cesium.Cartesian3()
        );
      Cesium.Cartesian3.normalize(satToCylinder, satToCylinder);
      return calculateBeamOrientation(satToCylinder);
      }, false);

      const beamEntity = this.viewer.entities.add({
        name: radarId,
        id: radarId,
        show: true,
        position: cylinderPosition,
        orientation: cylinderOrientation,
        cylinder: {
          length: Math.sqrt(height * height + cell.dx * cell.dx + cell.dy * cell.dy),
          topRadius: 0,
          bottomRadius: this.beamRadius,
          material: beamColor.withAlpha(beamOpacity),
          outline: false,
          numberOfVerticalLines: 0,
          slices: 32,
        },
        label: {
          font: '16px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30)
        }
      });

      entities.push(beamEntity);
    });

    return entities;
  }

  public clearAll(): void {
    // 清除波束 - 从viewer中完全删除实体
    this.beamEntities.forEach(entity => {
      if (entity) {
        this.viewer.entities.remove(entity);
      }
    });
    this.beamEntities = [];

    // 清除小区
    this.clearCells();
    this.cellCenters = [];
    this.currentCenterCellIndex = 0;
    this.beamPositions = [];
    this.cellPrimitive = null;

    this.selectedSatelliteId = null;
  }
}

// 导出默认类
export default BeamCellManager;
