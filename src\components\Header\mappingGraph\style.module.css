.mappingGraphModal {
  color: #fff !important;
}

.mappingGraphModal * {
  color: #fff !important;
}

.mappingGraphModal .ant-modal-content {
  background: #1a1a1a;
  border: 1px solid #333;
}

.mappingGraphModal .ant-modal-header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
}

.mappingGraphModal .ant-modal-title {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.mappingGraphModal .ant-btn-primary {
  color: #fff !important;
}

.mappingGraphModal .ant-modal-close-x,
.mappingGraphModal .ant-modal-close-icon,
.mappingGraphModal .ant-modal-close .anticon,
.mappingGraphModal .ant-modal-close .anticon svg,
.mappingGraphModal .ant-modal-close,
.mappingGraphModal .ant-modal-close:hover,
.mappingGraphModal .ant-modal-close:focus {
  color: #fff !important; /* 设置为白色 */
  fill: #fff !important; /* 确保SVG图标也是白色 */
}

.mappingGraphModal .ant-modal-body {
  background: #1a1a1a;
  padding: 0;
}

/* 主容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 400px;
}

/* 上半部分 - 模式图片展示区域 */
.modeImageSection {
  flex: 0 0 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #333;
  background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
}

.modeImage {
  max-height: 300px;
  max-width: 95%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 下半部分 - KVM节点展示区域 */
.kvmSection {
  flex: 1;
  display: flex;
  padding: 24px 12px;
  min-height: 120px;
  align-items: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
}

/* KVM节点项 */
.kvmItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  margin: 0 8px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.kvmItem:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(23, 126, 252, 0.3);
  transform: translateY(-2px);
}

/* 空节点项 */
.emptyKvmItem {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  margin: 0 8px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
}

/* 图标容器 */
.iconContainer {
  margin-bottom: 8px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconPlaceholder {
  height: 60px;
}

.nodeIcon {
  max-height: 60px;
  max-width: 90%;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 标签样式 */
.kvmLabel {
  min-height: 32px;
  width: 90%;
  margin: 4px 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  background: rgba(23, 126, 252, 0.2);
  border: 1px solid rgba(23, 126, 252, 0.3);
  font-weight: 500;
}

.nodeLabel {
  min-height: 32px;
  width: 90%;
  margin: 4px 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  font-weight: 500;
}

/* 空数据提示 */
.emptyDataContainer {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 22px;
  text-align: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
}

.emptyText {
  color: #bbb;
  font-size: 18px;
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mappingGraphModal {
    width: 90% !important;
  }
  
  .kvmSection {
    padding: 16px 8px;
  }
  
  .kvmItem,
  .emptyKvmItem {
    margin: 0 4px;
    padding: 8px;
  }
  
  .kvmLabel,
  .nodeLabel {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .mappingGraphModal {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .container {
    height: auto;
    min-height: 400px;
  }
  
  .modeImageSection {
    flex: 0 0 150px;
  }
  
  .modeImage {
    max-height: 200px;
  }
  
  .kvmSection {
    flex-direction: column;
    padding: 12px;
  }
  
  .kvmItem,
  .emptyKvmItem {
    width: 100%;
    margin: 4px 0;
    flex-direction: row;
    min-height: 60px;
  }
  
  .iconContainer {
    margin-right: 12px;
    margin-bottom: 0;
    height: 30px;
  }
  
  .nodeIcon {
    max-height: 40px;
  }
  
  .kvmLabel,
  .nodeLabel {
    font-size: 14px;
    margin: 0 4px;
    width: auto;
    flex: 1;
  }
}