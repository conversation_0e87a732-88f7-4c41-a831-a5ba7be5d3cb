import { basisBackendUrl, POST } from "../../utils/api/basicURL";

/**
 * 获取当前星间链路上的卫星响应接口
 */
export interface GetPathSatelliteExternalResponse {
  slot_index: number;
  satellites: number[];
  satellite_count: number;
  request_time_ns: number;
}

/**
 * 获取当前星间链路上的卫星
 * @param time - 时间戳（纳秒）
 * @returns Promise<GetPathSatelliteExternalResponse>
 */
export async function getPathSatelliteExternal(time: number): Promise<GetPathSatelliteExternalResponse> {
  try {
    const requestData = {
      time: time
    };
    
    const response = await POST(basisBackendUrl + "/get_path_satellite_external", requestData);
    return response;
  } catch (error) {
    console.error('Get path satellite external error:', error);
    throw error;
  }
}
