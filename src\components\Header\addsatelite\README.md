# 星座管理组件

这个组件提供了三种方式来管理卫星星座数据：

## 功能特性

### 1. 星座文件导入
- 上传TLE文件（轨道数据）
- 上传ISL文件（星间链路配置）
- 支持 .tle, .txt, .isl, .json 格式

### 2. 星座模版构建
- 通过参数配置创建星座
- 包含轨道数量、卫星数量、高度、倾斜度等参数
- 支持多种ISL配置选项

### 3. 定时接收（新增功能）
- 设置TLE数据源URL
- 配置更新间隔（秒）
- 支持定时上传和取消任务

## API接口

### 定时任务相关接口

#### 设置定时任务
```typescript
scheduleTleUpdate(tleUrl: string, intervalInSeconds: number): Promise<ApiResponse>
```

**参数：**
- `tleUrl`: TLE数据源的URL地址
- `intervalInSeconds`: 更新间隔，单位为秒（最小60秒，最大86400秒）

**请求格式：**
```json
{
  "tle_url": "https://example.com/tle/data.tle",
  "interval": 3600,
  "unit": "seconds"
}
```

#### 取消定时任务
```typescript
cancelTleUpdate(): Promise<ApiResponse>
```

**请求格式：**
```json
{}
```

## 使用示例

```tsx
import AddSatellite from './components/Header/addsatelite';

function App() {
  const [visible, setVisible] = useState(false);

  const handleSubmit = (data: any) => {
    if (data.type === 'scheduled') {
      console.log('定时任务设置成功:', data);
    }
    // 处理其他类型...
  };

  return (
    <AddSatellite
      visible={visible}
      onClose={() => setVisible(false)}
      onSubmit={handleSubmit}
    />
  );
}
```

## 文件结构

```
addsatelite/
├── index.tsx          # 主组件
├── uploadAPI.ts       # API接口函数
├── style.module.css   # 样式文件
├── demo.tsx          # 演示组件
├── test.tsx          # 测试文件
└── README.md         # 说明文档
```

## 注意事项

1. 定时接收功能需要后端支持 `/schedule_tle_update` 和 `/cancel_tle_update` 接口
2. TLE数据源URL必须是有效的HTTP/HTTPS地址
3. 更新间隔建议设置为合理的值，避免过于频繁的请求
4. 取消任务会停止所有正在运行的定时更新任务
