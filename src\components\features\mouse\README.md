# 链路状态API模块

这个模块提供了获取和处理卫星链路状态信息的功能。

## 文件结构

- `linkStatusAPI.ts` - 链路状态API的核心实现
- `mouseHandlers.ts` - 鼠标事件处理器，已集成链路状态API
- `types.ts` - 类型定义
- `index.ts` - 模块导出

## API接口

### 后端接口

**路径**: `/api/get_link_status_gen`
**方法**: GET
**参数**: 无

**返回格式**:
```json
{
  "data": {
    "availability": "available",
    "load": 45.32,
    "signal_strength": -60.15
  },
  "message": "Success",
  "status": 200
}
```

### 主要函数

#### `getLinkStatus()`
获取原始链路状态数据
```typescript
const rawData = await getLinkStatus();
```

#### `getProcessedLinkStatus()`
获取处理后的链路状态数据，适合直接显示
```typescript
const processedData = await getProcessedLinkStatus();
```

## 鼠标悬停行为

当鼠标悬停在链路上时：
1. **立即请求**: 第一次悬停时立即调用API获取数据
2. **定时更新**: 持续悬停时每2秒自动更新一次数据
3. **停止更新**: 鼠标移开时停止定时更新

## 数据格式

### 原始数据 (LinkStatusResponse)
```typescript
interface LinkStatusResponse {
  availability: string;    // "available", "unavailable", "degraded"
  load: number;           // 链路负载百分比 (0-100)
  signal_strength: number; // 信号强度 (dBm)
}
```

### 处理后数据 (ProcessedLinkStatus)
```typescript
interface ProcessedLinkStatus {
  signalStrength: string;     // "−60.15 dBm"
  linkLoadPercentage: string; // "45.32%"
  linkStatus: string;         // "正常", "异常", "降级", "未知"
  linkStatusColor: string;    // 对应的颜色代码
}
```

## 状态映射

| availability | linkStatus | linkStatusColor |
|-------------|------------|-----------------|
| available   | 正常       | #00ff00         |
| unavailable | 异常       | #ff0000         |
| degraded    | 降级       | #ffff00         |
| 其他        | 未知       | #888888         |

## 缓存机制

- **缓存时间**: 2秒
- **自动刷新**: 当缓存过期时自动调用API更新
- **错误处理**: API调用失败时使用缓存数据（如果有）

## 使用示例

### 在鼠标悬停时显示链路信息
```typescript
import { linkStatusCache } from './linkStatusAPI';

// 在鼠标移动事件中
linkStatusCache.getCachedLinkStatus().then((linkData) => {
  tooltipElement.innerHTML = `
    <div>信号强度：${linkData.signalStrength}</div>
    <div>链路负载：${linkData.linkLoadPercentage}</div>
    <div style="color: ${linkData.linkStatusColor};">
      可用性：${linkData.linkStatus}
    </div>
  `;
});
```

### 手动获取链路状态
```typescript
import { getProcessedLinkStatus } from './linkStatusAPI';

try {
  const linkData = await getProcessedLinkStatus();
  console.log('链路状态:', linkData);
} catch (error) {
  console.error('获取链路状态失败:', error);
}
```

## 测试

运行测试组件来验证API功能：
```typescript
import LinkStatusAPITest from '../../../test/LinkStatusAPITest';
```

## 错误处理

- API调用失败时会显示默认的"N/A"值
- 缓存机制确保在网络问题时仍能显示之前的数据
- 所有错误都会记录到控制台

## 配置

可以在 `linkStatusAPI.ts` 中修改以下配置：
- `ipAddress`: 后端服务器IP地址
- `CACHE_DURATION`: 缓存持续时间（毫秒）
- 状态映射规则

## 注意事项

1. 确保后端API服务正在运行
2. 检查网络连接和CORS设置
3. API调用是异步的，需要适当的错误处理
4. 缓存机制有助于提高性能，但可能导致数据稍有延迟
