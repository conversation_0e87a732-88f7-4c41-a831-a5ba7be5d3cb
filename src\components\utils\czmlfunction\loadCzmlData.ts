// @ts-nocheck
import { BaseStation } from "../../types/type";
import { setSelectedConstellation, getTerminalPathCzml} from "../api/postAPI";
import { getCZMLData, getConstellationList,GetPathCZMLExternal} from "../api/getListAPI";
import { setParameterSet, validateNetworkModeConfig, generateKvmList, type NetworkModeConfig } from "../../Header/settingsatellite/parameterSetAPI";
import useSimulationStore from "../../../store/simulationStore";
import { notification } from 'antd';


// 加载CZML数据功能的参数接口
export interface LoadCzmlDataParams {
  viewer: any;
  czmlDataSource: any;
  setIsLoading: (loading: boolean) => void;
  setSimulationConstellationName: (name: string) => void;
  setBaseStationList: (list: BaseStation[]) => void;
  setSatelliteList: (list: string[]) => void;
  setSimulationRunning: (running: boolean) => void;
}

// 位置信息接口
export interface PositionInfo {
  position1: number;
  position2: number;
  position1Name: string;
  position2Name: string;
  // 新增网络模式配置信息
  networkModeConfig?: NetworkModeConfig;
}

// 加载CZML数据
export const loadCzmlData = async (
  satelliteName: string,
  params: LoadCzmlDataParams,
  positionInfo?: PositionInfo
) => {
  const {
    viewer,
    czmlDataSource,
    setIsLoading,
    setSimulationConstellationName,
    setBaseStationList,
    setSatelliteList,
    setSimulationRunning
  } = params;

  // 显示loading动画
  setIsLoading(true);
  
  // 保存仿真开始时的星座名称
  setSimulationConstellationName(satelliteName);
  
  // 确保viewer和必要的对象存在
  if (!viewer || !viewer.entities || !viewer.dataSources) {
    console.error("Cesium viewer未正确初始化");
    setIsLoading(false);
    return;
  }
  
  try {













    console.log("开始通过HTTP请求加载CZML数据...");
    console.log("当前卫星名称:", satelliteName);
    console.log("位置信息:", positionInfo);
    
    // 根据卫星名称确定星座索引（从0开始）
    const getConstellationIndex = async (satName: string): Promise<number> => {
      try {
        // 从服务器获取星座列表
        const constellationData = await getConstellationList();
        // console.log("获取到的星座列表:", constellationData);
        
        // 在星座列表中查找匹配的星座
        const index = constellationData.findIndex(constellation => {
          // 转换为小写进行比较，支持部分匹配
          const constellationLower = constellation.toLowerCase();
          const satNameLower = satName.toLowerCase();
          
          // 检查星座名称是否包含卫星名称，或者卫星名称是否包含星座名称
          return constellationLower.includes(satNameLower) || satNameLower.includes(constellationLower);
        });
        
        if (index !== -1) {
          console.log(`找到匹配的星座: ${constellationData[index]}, 索引: ${index}`);
          return index;
        } else {
          console.warn(`未找到匹配的星座: ${satName}, 使用默认索引 0`);
          console.warn("可用的星座列表:", constellationData);
          return 0; // 默认返回第一个星座
        }
      } catch (error) {
        console.error("获取星座列表失败:", error);
        console.warn(`获取星座列表失败，对于卫星 ${satName} 使用默认索引 0`);
        return 0; // 如果获取失败，返回默认索引
      }
    };
    
    const constellationIndex = await getConstellationIndex(satelliteName);
    console.log(`星座索引: ${constellationIndex}`);
    
    // 第一步：发送setSelectedConstellation请求
    console.log(`发送setSelectedConstellation请求，索引: ${constellationIndex+1}`);
    const setResult = await setSelectedConstellation(constellationIndex+1);
    console.log("setSelectedConstellation请求完成:", setResult);











    
    // 第二步：等待请求完成后，发送getCZMLData请求
    console.log("发送getCZMLData请求...");
    const czmlResponse = await getCZMLData();
    console.log("getCZMLData请求完成:", czmlResponse);
    
    // 检查返回数据格式
    if (!czmlResponse || !czmlResponse.czml_data || !Array.isArray(czmlResponse.czml_data) || czmlResponse.czml_data.length < 2) {
      throw new Error("CZML数据格式不正确或数据不完整");
    }
    
    const satelliteData = czmlResponse.czml_data[0]; // [0]是satellite数据
    const groundStationData = czmlResponse.czml_data[1]; // [1]是ground_station数据
    
    console.log("卫星数据长度:", satelliteData ? satelliteData.length : 0);
    console.log("地面站数据长度:", groundStationData ? groundStationData.length : 0);
    
    let nowSatelliteList: string[] = [];
    let baseStationTemp: BaseStation[] = [];
    
    // 保存数据源引用，用于后续路径数据处理
    let satelliteDataSource = null;
    let groundDataSource = null;












     // 处理地面站数据
     if (groundStationData) {
      console.log("开始处理地面站数据...");




      // const czmlDataSource = new Cesium.CzmlDataSource();
      const grounddataSource = await czmlDataSource.process(groundStationData);
      groundDataSource = grounddataSource; // 保存引用
      // 遍历所有地面站实体并根据位置信息设置不同图标
      
      const groundEntities = grounddataSource.entities.values;
      console.log('groundEntitieslen',groundEntities.length);
      for (let i = 0; i < groundEntities.length; i++) {
        const entity = groundEntities[i];

        // console.log('groundEntity',entity.name);
        
        if (entity.name.includes("Ground Station/")) {
          // 根据位置信息判断应该使用哪个图标
          if (positionInfo) {
            if (entity.name === "Ground Station/"+positionInfo.position1) {
              // 位置1使用手机图标
              entity.billboard.image = '/images/phone.svg';  // 手机图标
              entity.billboard.scale = 0.2;
              console.log(`地面站 ${entity.id} (位置1) 设置为手机图标`);
            } else if (entity.name === "Ground Station/"+positionInfo.position2) {
              // 位置2使用地面站图标
              entity.billboard.image = '/images/ground.svg';  // 地面站图标
              entity.billboard.scale = 0.2;
              console.log(`地面站 ${entity.name} (位置2) 设置为地面站图标`);
            }
          }
          
          // 设置通用属性
          entity.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
          entity.billboard.horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
        }
      }
      
      // viewer.dataSources.add(grounddataSource);
      console.log("地面站数据处理完成");
    }


    
    // 处理卫星数据
    if (satelliteData) {
      console.log("开始处理卫星数据...");
      
      const satdataSource = await czmlDataSource.process(satelliteData);
      satelliteDataSource = satdataSource; // 保存引用
      // 遍历所有实体并修改卫星图标
      const entities = satdataSource.entities.values;
      for (let i = 0; i < entities.length; i++) {

        // if(entities[i].id.includes("Satellite/")) {}
        
        const entity = entities[i];
        // console.log('entity.id',entity.id);
        // console.log('entity.name',entity.name);
        if(entity.name.includes("Satellite/")) {

        // 根据entity.id判断使用哪个图片，默认图片为卫星图标，默认缩放比例为0.15
        let imagePath = '/images/statellite.png'; // 默认图片
        let scale = 0.15;

        // 从网络配置中获取核心网卫星和UPF卫星的编号，以及网络模式
        // 只有在网络模式3和4时才应用特殊卫星图标
        if (positionInfo && positionInfo.networkModeConfig) {
          const { mode, coreNetworkSatelliteId, upfSatelliteId } = positionInfo.networkModeConfig;

          // 只有网络模式3和4才需要特殊的卫星图标
          if (mode === '3' || mode === '4') {
            // 从entity.id中提取卫星编号（假设entity.id格式为数字或包含数字）
            const satelliteIdMatch = entity.id.toString().match(/\d+/);
            if (satelliteIdMatch) {
              const satelliteNumber = satelliteIdMatch[0];

              // 网络模式3：需要核心网卫星和UPF卫星
              if (mode === '3') {
                // 判断是否为核心网卫星
                if (coreNetworkSatelliteId && satelliteNumber === coreNetworkSatelliteId) {
                  imagePath = '/images/5gcore.png';
                  console.log(`卫星 ${entity.id} 设置为核心网卫星图标 (模式${mode})`);
                  scale = 0.35;
                }
                // 判断是否为UPF卫星
                else if (upfSatelliteId && satelliteNumber === upfSatelliteId) {
                  imagePath = '/images/5gUPF.png';
                  console.log(`卫星 ${entity.id} 设置为UPF卫星图标 (模式${mode})`);
                  scale = 0.35;
                }
              }
              // 网络模式4：只需要UPF卫星
              else if (mode === '4') {
                // 判断是否为UPF卫星
                if (upfSatelliteId && satelliteNumber === upfSatelliteId) {
                  imagePath = '/images/5gUPF.png';
                  console.log(`卫星 ${entity.id} 设置为UPF卫星图标 (模式${mode})`);
                  scale = 0.35;
                }
              }
            }
          }
        }
        
        
        
        if (entity.billboard) {
          entity.billboard.image = imagePath;
          entity.billboard.scale = scale;
          entity.billboard.verticalOrigin = Cesium.VerticalOrigin.CENTER;
          entity.billboard.horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
          
          // 性能优化设置
          entity.billboard.sizeInMeters = false;
          entity.billboard.disableDepthTestDistance = 10000000; // 10000km内启用深度测试
          
          // 距离缩放以提高远距离性能
          entity.billboard.scaleByDistance = new Cesium.NearFarScalar(
            1000000,  // 1000km处开始缩放
            1.0,      // 近距离比例
            20000000, // 20000km处
            0.3       // 远距离比例
          );
          
          // 像素偏移优化
          entity.billboard.pixelOffsetScaleByDistance = new Cesium.NearFarScalar(
            1000000, 1.0,
            20000000, 0.0
          );
        }
        
        // 添加到卫星列表
        nowSatelliteList.push([entity.id, true, false, false, false, false, false, {r: 255, g: 255, b: 255}, false]);
      }
    }
      // viewer.dataSources.add(satdataSource);
      console.log("卫星数据处理完成");
    }


                 // 调用参数设置API（如果提供了网络模式配置）
    if (positionInfo && positionInfo.networkModeConfig) {
      try {
        console.log("开始设置模拟参数...");

        // 验证网络模式配置
        const validation = validateNetworkModeConfig(positionInfo.networkModeConfig);
        if (!validation.isValid) {
          console.error("网络模式配置验证失败:", validation.errorMessage);
          throw new Error(validation.errorMessage);
        }

        // 生成KVM列表
        const kvmList = generateKvmList(positionInfo.networkModeConfig);

        // 存储网络模式配置和KVM列表到simulationStore
        const { setNetworkModeConfig, setKvmList } = useSimulationStore.getState();
        setNetworkModeConfig(positionInfo.networkModeConfig);
        setKvmList(kvmList);

        // console.log("网络模式配置已存储到simulationStore:", positionInfo.networkModeConfig);
        // console.log("KVM列表已存储到simulationStore:", kvmList);

        // 调用参数设置API，自动发送两次请求
        const parameterSetResult = await setParameterSet(positionInfo.networkModeConfig);
        if (parameterSetResult) {
          console.log("模拟参数设置成功:---",parameterSetResult);
        } else {
          console.error("模拟参数设置失败");
          // 参数设置失败不影响CZML数据加载，继续执行
        }
      } catch (paramError) {
        console.error("参数设置过程中出错:", paramError);
        // 参数设置失败不影响CZML数据加载，继续执行
      }
    }


      // 第三步：如果提供了位置信息，获取路径CZML数据
      if (positionInfo && positionInfo.position1 >= 0 && positionInfo.position2 >= 0) {
        try {
          console.log(`发送getTerminalPathCzml请求，位置1: ${positionInfo.position1} (${positionInfo.position1Name}), 位置2: ${positionInfo.position2} (${positionInfo.position2Name})`);
          // const pathCzmlData = await getTerminalPathCzml(positionInfo.position1, positionInfo.position2);
          const pathCzmlData = await GetPathCZMLExternal();


          // const czmlDataSource = new Cesium.CzmlDataSource();
          const pathDataSource =  await czmlDataSource.process(pathCzmlData);
          const pathEntities = pathDataSource.entities.values;
          console.log('pathEntitieslen',pathEntities.length);
          for (let i = 0; i < pathEntities.length; i++) {
            const entity = pathEntities[i];
            if (entity.polyline) {
              try {
                // 根据id判断颜色
                const color = entity.id && entity.id.includes("Ground") 
                  ? Cesium.Color.RED 
                  : Cesium.Color.LIME;
    
                entity.polyline.material = new Cesium.LineFlowMaterialProperty({
                  color: color,
                  speed: 50,
                  percent: 1,
                  gradient: 0.5,
                });

                // 为polyline实体添加properties字段，包含链路信息
                entity.addProperty("linkLoad");
                entity.addProperty("linkSpeed");
                entity.addProperty("packetLossRate");
                entity.addProperty("linkStatus");
                entity.addProperty("linkType");
                entity.addProperty("bandwidth");
                entity.addProperty("latency");
                entity.addProperty("signalStrength");
                entity.addProperty("linkLoadPercentage");

                // 根据链路类型设置不同的模拟数据
                const isGroundLink = entity.id && entity.id.includes("Ground");
                
                if (isGroundLink) {
                  // 地面链路数据
                  entity.linkLoad = `${Math.floor(Math.random() * 40 + 30)}%`; // 30-70%
                  entity.linkSpeed = `${Math.floor(Math.random() * 500 + 100)} Mbps`; // 100-600 Mbps
                  entity.packetLossRate = `${(Math.random() * 0.5 + 0.1).toFixed(2)}%`; // 0.1-0.6%
                  entity.linkStatus = "正常";
                  entity.linkType = "地面链路";
                  entity.bandwidth = `${Math.floor(Math.random() * 200 + 300)} MHz`; // 300-500 MHz
                  entity.latency = `${Math.floor(Math.random() * 50 + 20)} ms`; // 20-70 ms
                  entity.signalStrength = `${Math.floor(Math.random() * 30 + 50)} dBm`; // 50-80 dBm
                  entity.linkLoadPercentage = `${Math.floor(Math.random() * 40 + 30)}%`; // 30-70%
                } else {
                  // 星间链路数据
                  entity.linkLoad = `${Math.floor(Math.random() * 30 + 20)}%`; // 20-50%
                  entity.linkSpeed = `${Math.floor(Math.random() * 2000 + 1000)} Mbps`; // 1000-3000 Mbps
                  entity.packetLossRate = `${(Math.random() * 0.2 + 0.05).toFixed(2)}%`; // 0.05-0.25%
                  entity.linkStatus = "正常";
                  entity.linkType = "星间链路";
                  entity.bandwidth = `${Math.floor(Math.random() * 1000 + 500)} MHz`; // 500-1500 MHz
                  entity.latency = `${Math.floor(Math.random() * 10 + 5)} ms`; // 5-15 ms
                  entity.signalStrength = `${Math.floor(Math.random() * 20 + 60)} dBm`; // 60-80 dBm
                  entity.linkLoadPercentage = `${Math.floor(Math.random() * 30 + 20)}%`; // 20-50%
                }

                // console.log(`为链路 ${entity.id} 添加了属性信息`);
              } catch (e) {
                console.error(`Error applying LineFlowMaterialProperty to entity ${entity.id}:`, e);
              }
            }
          }
          await viewer.dataSources.add(pathDataSource);
          console.log("路径数据加载成功");
          setBaseStationList(baseStationTemp);
          setSatelliteList(nowSatelliteList);
          // 设置仿真已开始
          setSimulationRunning(true);
          console.log("所有CZML数据加载完成");

        
        } catch (pathError) {
          console.error("路径CZML数据加载出错:", pathError);

          // 检查是否为500状态码错误或HTTP错误状态码500
          if (pathError && pathError.message &&
              (pathError.message.includes('Status: 500') || pathError.message.includes('HTTP error! Status: 500'))) {
            // 显示错误通知
            notification.error({
              message: '网络模式不支持',
              description: '该星座暂不支持目前的网络模式，请重新设置',
              duration: 5
            });

            // 停止仿真并重置状态
            setSimulationRunning(false);
            setIsLoading(false);
            throw new Error('该星座暂不支持目前的网络模式，请重新设置');
          }

          // 其他错误，路径加载失败不影响主要数据的显示
        }
      }  

    // viewer.dataSources.add(pathDataSource);
    // 即使没有路径信息，也要更新状态
    setBaseStationList(baseStationTemp);
    setSatelliteList(nowSatelliteList);
    // 设置仿真已开始
    setSimulationRunning(true);
    console.log("卫星和地面站数据加载完成");
    console.log('viewer.animation.viewModel.multiplier',viewer.animation.viewModel.multiplier);
    viewer.clock.multiplier= 5;
    console.log('viewer.clock.multiplier',viewer.clock.multiplier); 
    
  } catch (error) {
    console.error("HTTP CZML数据加载出错:", error);
    // 加载失败时，确保仿真状态不会被设置为运行中
    setSimulationRunning(false);
    throw error;
  } finally {
    // 无论成功还是失败，都隐藏loading动画
    setIsLoading(false);
  }
}; 