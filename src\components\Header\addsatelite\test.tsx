import React from 'react';
import { render, screen } from '@testing-library/react';
import AddSatellite from './index';

// 简单的测试来验证组件能正常渲染
describe('AddSatellite Component', () => {
  test('renders all three tabs', () => {
    const mockProps = {
      visible: true,
      onClose: jest.fn(),
      onSubmit: jest.fn(),
    };

    render(<AddSatellite {...mockProps} />);
    
    // 检查三个标签页是否存在
    expect(screen.getByText('星座文件导入')).toBeInTheDocument();
    expect(screen.getByText('星座模版构建')).toBeInTheDocument();
    expect(screen.getByText('定时接收')).toBeInTheDocument();
  });

  test('renders scheduled tab content', () => {
    const mockProps = {
      visible: true,
      onClose: jest.fn(),
      onSubmit: jest.fn(),
    };

    render(<AddSatellite {...mockProps} />);
    
    // 点击定时接收标签
    const scheduledTab = screen.getByText('定时接收');
    scheduledTab.click();
    
    // 检查定时接收表单元素是否存在
    expect(screen.getByText('TLE数据源URL')).toBeInTheDocument();
    expect(screen.getByText('更新间隔 (秒)')).toBeInTheDocument();
    expect(screen.getByText('定时上传')).toBeInTheDocument();
    expect(screen.getByText('取消任务')).toBeInTheDocument();
  });
});
