.header {
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  max-width: 100vw;
  height: 7vw;
  max-height: 9vw;
  overflow: hidden; /* 确保整个header不会产生滚动 */

  align-items: flex-start;
  justify-items: center;

  /* 整体透明度 */
  opacity: 0.9;  

  z-index: 1000;
}

.headerBackground {
  position: absolute;
  width: 100%;
  max-width: 100%;
  height: 50%;
  max-height: 50%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden; /* 防止shimmer动画超出边界 */

  backdrop-filter: blur(4px); /* 模糊背景，让更"玻璃感" */

  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow:
  0 0 8px rgba(0, 255, 255, 0.3),  /* 外发光青色 */
  inset 0 0 8px rgba(0, 255, 255, 0.2); /* 内发光 */

  background: 
    repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.02),
      rgba(255, 255, 255, 0.02) 2px,
      transparent 2px,
      transparent 6px
    ),
    linear-gradient(135deg, #00203f, #003366, #1f5f9f);

  z-index: 1000;
}

.headerBackground::before {
  content: "";
  position: absolute;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: rotate(0deg);
  animation: shimmer 4s infinite linear;
  pointer-events: none;
  z-index: 0;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(0deg);
  }
  100% {
    transform: translateX(100%) rotate(0deg);
  }
}

.logoBlock {
  align-items: center;
  justify-content: center;
  width: 20%;
  max-width: 20%;
  height: 100%;
  max-height: 100%;
  display: flex;
  gap: 10px;
  padding: 10px;
}

.logoBlock img {
  height: 50px;
  object-fit: contain;
}

.backTitleBlock {
  width: 20%;
  max-width: 20%;
  height: 100%;
  max-height: 100%;
}

.slot {
  display: flex;
  align-items: center;
  justify-content: center;
}

.leftButtons {
  width: 20%;
  max-width: 20%;
  height: 100%;
  max-height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.leftButtons .slot {
  width: 50%;
  max-width: 50%;
  height: 100%;
  max-height: fit-content;
}

.titleBlock {
  background: url("../../assets/components/header_title.svg");
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center top;

  position: absolute;
  left: 50%;
  top: 0%;
  transform: translate(-50%, -5%);
  
  width: 27%;
  max-width: 27%;
  height: 80%;
  max-height: 80%;

  text-align: center;
  z-index: 1001;
}

.titleBlock .text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  max-width: 100%;
  white-space: nowrap; /* 防止文字换行 */
}

.titleBlock .text h1 {
  margin: 0;
  font-size: 26px;
  font-weight: bold;
  letter-spacing: 2px;
  color: #ffffff !important; /* 确保白色文字优先级 */
  text-shadow: 0 0 10px rgb(255, 255, 255), /* 发光效果 */
                  0 0 20px rgb(182, 201, 255),
                  0 0 30px rgb(70, 125, 255),
                  0 0 40px rgb(33, 99, 255);
}

.rightButtons {
  width: 30%;
  max-width: 30%;
  height: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  /* justify-content: flex-start; */
}

.rightButtons .slot {
  width: 25%;
  align-items: center;
} 