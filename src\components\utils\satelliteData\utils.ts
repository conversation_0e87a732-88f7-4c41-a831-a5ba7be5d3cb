// 卫星数据处理工具函数

import { Position, Velocity, SatelliteData, GroundStationData } from './types';
import { 
  EARTH_RADIUS_KM, 
  METERS_TO_KM, 
  DEFAULT_ALTITUDE_RANGE, 
  VELOCITY_RANGE, 
  POSITION_RANGE, 
  MOCK_GROUND_STATIONS 
} from './constants';

/**
 * 获取当前时间（从外部传入，避免直接访问全局viewer）
 * @param currentTime - 从外部传入的当前时间
 */
export function getCurrentTime(currentTime?: string): string {
  if (currentTime) {
    return currentTime;
  }
  // 后备使用系统时间
  console.log('⚠️ No time provided, using system time');
  return new Date().toISOString();
}

/**
 * 转换位置数据（从米转换为千米）
 */
export function convertPosition(position: Position): Position {
  return {
    x: position.x / METERS_TO_KM,
    y: position.y / METERS_TO_KM,
    z: position.z / METERS_TO_KM
  };
}

/**
 * 转换速度数据（从m/s转换为km/s）
 */
export function convertVelocity(velocity: Velocity): Velocity {
  return {
    x: (velocity?.x || 0) / METERS_TO_KM,
    y: (velocity?.y || 0) / METERS_TO_KM,
    z: (velocity?.z || 0) / METERS_TO_KM
  };
}

/**
 * 计算海拔高度
 */
export function calculateAltitude(position: Position): number {
  const magnitude = Math.sqrt(
    Math.pow(position.x, 2) + 
    Math.pow(position.y, 2) + 
    Math.pow(position.z, 2)
  );
  
  return magnitude / METERS_TO_KM - EARTH_RADIUS_KM;
}

/**
 * 格式化相邻卫星数组
 */
export function formatAdjacentSatellites(adjacentSatellites: any[]): string[] {
  if (!Array.isArray(adjacentSatellites)) {
    console.log('adjacentSatellites is not an array');
    return [];
  }
  
  return adjacentSatellites.map(satId => `Satellite ${satId}`);
}

/**
 * 格式化已连接地面站数组
 */
export function formatConnectedGroundStations(connectedGroundStations: any[]): string[] {
  if (!Array.isArray(connectedGroundStations)) {
    console.log('connectedGroundStations is not an array');
    return [];
  }
  
  return connectedGroundStations.map(gsId => `Ground Station ${gsId}`);
}

/**
 * 转换后端数据为前端格式
 */
export function transformBackendData(backendData: any, satelliteId: string): SatelliteData {
  const data = backendData.data;
  
  return {
    satellite_id: satelliteId,
    position: convertPosition(data.position),
    velocity: convertVelocity(data.velocity),
    altitude: calculateAltitude(data.position),
    ground_stations: formatConnectedGroundStations(data.connected_ground_stations || []),
    adjacent_satellites: formatAdjacentSatellites(data.adjacent_satellites || []),
    status: '实时数据',
    last_updated: new Date().toLocaleString(),
    calculation_method: data.calculation_method || 'unknown'
  };
}

/**
 * 转换地面站后端数据为前端格式
 */
export function transformGroundStationData(backendData: any, groundStationId: string): GroundStationData {
  const data = backendData.data;
  
  return {
    ground_station_id: groundStationId,
    position: data.position || { latitude: 0, longitude: 0, altitude: 0 },
    connected_satellites: data.connected_satellites || [],
    beam_connections: data.beam_connections || [],
    total_capacity: data.total_capacity || 0,
    used_capacity: data.used_capacity || 0,
    active_connections: data.active_connections || 0,
    status: '实时数据',
    last_updated: new Date().toLocaleString()
  };
}

/**
 * 生成字符串哈希码
 */
export function hashCode(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

/**
 * 创建种子随机数生成器
 */
export function seededRandom(seed: number): () => number {
  let current = seed;
  return function() {
    current = (current * 9301 + 49297) % 233280;
    return current / 233280;
  };
}

/**
 * 生成模拟位置数据
 */
export function generateMockPosition(rng: () => number): Position {
  const range = POSITION_RANGE;
  return {
    x: (rng() - 0.5) * (range.max - range.min),
    y: (rng() - 0.5) * (range.max - range.min),
    z: (rng() - 0.5) * (range.max - range.min)
  };
}

/**
 * 生成模拟速度数据
 */
export function generateMockVelocity(rng: () => number): Velocity {
  const range = VELOCITY_RANGE;
  return {
    x: (rng() - 0.5) * (range.max - range.min),
    y: (rng() - 0.5) * (range.max - range.min),
    z: (rng() - 0.5) * (range.max - range.min)
  };
}

/**
 * 生成模拟海拔数据
 */
export function generateMockAltitude(rng: () => number): number {
  const range = DEFAULT_ALTITUDE_RANGE;
  return range.min + rng() * (range.max - range.min);
}

/**
 * 获取模拟地面站数据
 */
export function getMockGroundStations(rng: () => number): string[] {
  const stations = MOCK_GROUND_STATIONS;
  const count = Math.floor(rng() * 3); // 0-2 ground stations
  const result: string[] = [];
  
  for (let i = 0; i < count; i++) {
    const station = stations[Math.floor(rng() * stations.length)];
    if (!result.includes(station)) {
      result.push(station);
    }
  }
  
  return result;
}

/**
 * 获取模拟相邻卫星
 */
export function getMockAdjacentSatellites(satelliteId: string, rng: () => number): string[] {
  const num = parseInt(satelliteId) || 0;
  const candidates = [
    `Satellite ${num + 1}`,
    `Satellite ${num - 1}`,
    `Satellite ${num + 10}`,
    `Satellite ${num - 10}`
  ];
  
  return candidates.filter(() => rng() > 0.4);
}

/**
 * 生成模拟卫星数据
 */
export function generateMockSatelliteData(satelliteId: string): SatelliteData {
  const seed = hashCode(satelliteId);
  const rng = seededRandom(seed);
  
  return {
    satellite_id: satelliteId,
    position: generateMockPosition(rng),
    velocity: generateMockVelocity(rng),
    altitude: generateMockAltitude(rng),
    ground_stations: getMockGroundStations(rng),
    adjacent_satellites: getMockAdjacentSatellites(satelliteId, rng),
    status: '模拟数据',
    last_updated: new Date().toLocaleString()
  };
}

/**
 * 生成模拟地面站数据
 */
export function generateMockGroundStationData(groundStationId: string): GroundStationData {
  const seed = hashCode(`gs_${groundStationId}`);
  const rng = seededRandom(seed);
  
  // 生成现实的地面站坐标
  const latitude = (rng() - 0.5) * 160; // -80 to 80 degrees
  const longitude = (rng() - 0.5) * 360; // -180 to 180 degrees
  const altitude = rng() * 1000 + 100; // 100-1100 meters
  
  const connectedSatellites = [];
  const numConnections = Math.floor(rng() * 5) + 1; // 1-5 connections
  for (let i = 0; i < numConnections; i++) {
    connectedSatellites.push(`Satellite ${Math.floor(rng() * 1000) + 1}`);
  }
  
  const totalCapacity = Math.floor(rng() * 5000) + 1000; // 1000-6000 Mbps
  const usedCapacity = Math.floor(rng() * totalCapacity * 0.8); // Up to 80% utilization
  
  return {
    ground_station_id: groundStationId,
    position: {
      latitude: latitude,
      longitude: longitude,
      altitude: altitude
    },
    connected_satellites: connectedSatellites,
    beam_connections: [],
    total_capacity: totalCapacity,
    used_capacity: usedCapacity,
    active_connections: connectedSatellites.length,
    status: '模拟数据',
    last_updated: new Date().toLocaleString()
  };
}
