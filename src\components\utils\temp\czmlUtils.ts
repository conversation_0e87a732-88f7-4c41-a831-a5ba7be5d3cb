/**
 * CZML 工具类，用于管理 CZML 对象
 */
class CzmlUtils {
    private czmlObjectMap: Map<string, any>;

    constructor() {
        this.czmlObjectMap = new Map<string, any>();
    }

    /**
     * 添加 CZML 对象
     * @param czmlName - CZML 对象名称
     * @param czmlObject - CZML 对象数据
     */
    addCZMLObject(czmlName: string, czmlObject: any): void {
        this.czmlObjectMap.set(czmlName, czmlObject);
    }

    /**
     * 获取 CZML 对象
     * @param czmlName - CZML 对象名称
     * @returns CZML 对象数据或 undefined
     */
    getCZMLObject(czmlName: string): any | undefined {
        return this.czmlObjectMap.get(czmlName);
    }

    // deleteCZMLObject(czmlName: string): void {
    //     const czmlObject = this.czmlObjectMap.get(czmlName);
    //     if (czmlObject) {
    //         czmlObject.destroy();
    //         this.czmlObjectMap.delete(czmlName);
    //     }
    // }
}

export { CzmlUtils };
