// 地面站相关的API接口
import { basisBackendUrl, POST } from '../../utils/api/basicURL';

// 地面站文件上传API响应类型
export interface GroundStationFileUploadResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: {
    groundStationId: string | number;
    fileName: string;
    uploadTime: string;
    processedCount?: number;
    groundStations?: GroundStationInfo[];
  };
}

// 地面站模版创建API响应类型
export interface GroundStationTemplateResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: {
    groundStationId: string | number;
    name: string;
    longitude: number;
    latitude: number;
    altitude: number;
    description: string;
    createdTime: string;
  };
}

// 地面站信息类型
export interface GroundStationInfo {
  id: string | number;
  name: string;
  longitude: number;
  latitude: number;
  altitude: number;
  description?: string;
  status?: 'active' | 'inactive' | 'maintenance';
  type?: 'primary' | 'backup' | 'mobile';
  capabilities?: string[];
  createdTime?: string;
  updatedTime?: string;
}

// 地面站模版创建数据类型
export interface GroundStationTemplateData {
  name: string;
  longitude: number;
  latitude: number;
  altitude?: number; // 可选字段，默认为0（米）
  description?: string;
  type?: 'primary' | 'backup' | 'mobile';
  capabilities?: string[];
}

/**
 * 地面站文件上传API
 * @param formData 包含地面站文件的FormData对象
 * @returns Promise<GroundStationFileUploadResponse>
 */
export async function uploadGroundStationFile(formData: FormData): Promise<GroundStationFileUploadResponse> {
  try {
    // TODO: 替换为实际的后端API端点
    const response = await fetch('/api/ground-station/upload', {
      method: 'POST',
      body: formData,
      headers: {
        // 不要设置 Content-Type，让浏览器自动设置 multipart/form-data
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('地面站文件上传失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 地面站模版创建API
 * @param data 地面站模版数据 (altitude字段可选，默认为0)
 * @returns Promise<GroundStationTemplateResponse>
 */
export async function createGroundStationFromTemplate(data: GroundStationTemplateData): Promise<GroundStationTemplateResponse> {
  try {
    // 确保altitude字段有默认值0
    const requestData = {
      name: data.name,
      longitude: data.longitude,
      latitude: data.latitude,
      altitude: 0  // 如果altitude未定义或为null，则设为0
    };

    // 使用正确的后端端点
    const apiUrl = `${basisBackendUrl}/add_groundstation`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    
    // 转换后端响应格式为前端期望的格式
    if (result.status === 200) {
      return {
        success: true,
        message: result.message,
        data: {
          groundStationId: result.data?.station_id || 'unknown',
          name: requestData.name,
          longitude: requestData.longitude,
          latitude: requestData.latitude,
          altitude: requestData.altitude,
          description: data.description || '',
          createdTime: new Date().toISOString()
        }
      };
    } else {
      return {
        success: false,
        error: result.message || '创建地面站失败'
      };
    }
    
  } catch (error) {
    console.error('地面站模版创建失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 获取地面站列表API
 * @returns Promise<{success: boolean, data?: GroundStationInfo[], error?: string}>
 */
export async function getGroundStationList(): Promise<{success: boolean, data?: GroundStationInfo[], error?: string}> {
  try {
    // TODO: 替换为实际的后端API端点
    const response = await fetch('/api/ground-station/list', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('获取地面站列表失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 删除地面站API
 * @param groundStationId 地面站ID
 * @returns Promise<{success: boolean, message?: string, error?: string}>
 */
export async function deleteGroundStation(groundStationId: string | number): Promise<{success: boolean, message?: string, error?: string}> {
  try {
    // TODO: 替换为实际的后端API端点
    const response = await fetch(`/api/ground-station/${groundStationId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('删除地面站失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 更新地面站信息API
 * @param groundStationId 地面站ID
 * @param data 更新的地面站数据
 * @returns Promise<GroundStationTemplateResponse>
 */
export async function updateGroundStation(groundStationId: string | number, data: Partial<GroundStationTemplateData>): Promise<GroundStationTemplateResponse> {
  try {
    // TODO: 替换为实际的后端API端点
    const response = await fetch(`/api/ground-station/${groundStationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
    
  } catch (error) {
    console.error('更新地面站失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

// 验证地面站数据格式
export function validateGroundStationData(data: GroundStationTemplateData): {isValid: boolean, error?: string} {
  // 验证必填字段
  if (!data.name || data.name.trim() === '') {
    return { isValid: false, error: '地面站名称不能为空' };
  }

  // 验证经度范围
  if (typeof data.longitude !== 'number' || data.longitude < -180 || data.longitude > 180) {
    return { isValid: false, error: '经度必须在-180到180之间' };
  }

  // 验证纬度范围
  if (typeof data.latitude !== 'number' || data.latitude < -90 || data.latitude > 90) {
    return { isValid: false, error: '纬度必须在-90到90之间' };
  }


  return { isValid: true };
}

// 模拟数据（用于开发测试）
export const mockGroundStationData: GroundStationInfo[] = [
  {
    id: 1,
    name: '北京地面站',
    longitude: 116.4074,
    latitude: 39.9042,
    altitude: 44,
    description: '北京主要地面站',
    status: 'active',
    type: 'primary',
    capabilities: ['数据接收', '指令发送', '遥测监控'],
    createdTime: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '上海地面站',
    longitude: 121.4737,
    latitude: 31.2304,
    altitude: 4,
    description: '上海备用地面站',
    status: 'active',
    type: 'backup',
    capabilities: ['数据接收', '遥测监控'],
    createdTime: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    name: '深圳地面站',
    longitude: 114.0579,
    latitude: 22.5431,
    altitude: 76,
    description: '深圳移动地面站',
    status: 'maintenance',
    type: 'mobile',
    capabilities: ['数据接收'],
    createdTime: '2024-01-03T00:00:00Z'
  }
]; 