import React, { useEffect, useRef, useState } from 'react';
import { Card, Slider, Switch, Row, Col } from 'antd';

// 使用全局Cesium对象
declare global {
  interface Window {
    Cesium: any;
  }
}

const Cesium = window.Cesium;

// 设置Cesium Ion token
Cesium.Ion.defaultAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJiYTg4MTUyNy0zMTA2LTRiMDktOGE1My05ZDA4OTRmOTE3YzciLCJpZCI6MTAzMjg1LCJpYXQiOjE2NTk0MDcyODB9.sfpT8e4oxun23JG--UmUN9ZD4SbQfU-Ljvh2MsPTTcY";

interface CellInfo {
  id: string;
  center: any;
  vertices: any[];
  isIlluminated: boolean;
  index: number; // 在几何实例数组中的索引
}

const BeamCellDemo: React.FC = () => {
  const cesiumContainerRef = useRef<HTMLDivElement>(null);
  const viewerRef = useRef<any>(null);
  const cellsRef = useRef<CellInfo[]>([]);
  const satelliteRef = useRef<any>(null);
  const beamRef = useRef<any>(null);
  const cellPrimitiveRef = useRef<any>(null);
  const geometryInstancesRef = useRef<any[]>([]);

  const [cellSize, setCellSize] = useState(500000); // 500km
  const [beamRadius, setBeamRadius] = useState(800000); // 800km
  const [showCells, setShowCells] = useState(true);
  const [showBeam, setShowBeam] = useState(true);
  const satelliteHeight = 550000; // 550km
  const [illuminatedCellsCount, setIlluminatedCellsCount] = useState(0);

  // 初始化Cesium
  useEffect(() => {
    if (!cesiumContainerRef.current) return;

    const viewer = new Cesium.Viewer(cesiumContainerRef.current, {
      shouldAnimate: true,
      infoBox: false,
      selectionIndicator: false,
      timeline: false,
      animation: false,
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      fullscreenButton: false,
      geocoder: false,
      terrainProvider: Cesium.createWorldTerrain()
    });

    viewerRef.current = viewer;

    // 设置初始视角
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 2000000), // 北京上空2000km
      orientation: {
        heading: 0.0,
        pitch: -Cesium.Math.PI_OVER_TWO,
        roll: 0.0
      }
    });

    // 创建地球表面的cell网格
    createEarthCells();
    
    // 创建卫星
    createSatellite();

    return () => {
      if (viewerRef.current) {
        viewerRef.current.destroy();
        viewerRef.current = null;
      }
    };
  }, []);

  // 创建地球表面的六边形cell网格 - 使用图元方式
  const createEarthCells = () => {
    if (!viewerRef.current) return;

    const viewer = viewerRef.current;
    cellsRef.current = [];
    geometryInstancesRef.current = [];

    // 清除现有的图元
    if (cellPrimitiveRef.current) {
      viewer.scene.primitives.remove(cellPrimitiveRef.current);
      cellPrimitiveRef.current = null;
    }

    // 清除现有的实体（卫星和波束）
    viewer.entities.removeAll();

    // 创建蜂窝网络六边形网格 - 紧密排列
    const hexRadius = cellSize / 111000; // 六边形半径（度）
    const hexHeight = hexRadius * 1.5; // 六边形高度
    const hexWidth = hexRadius * Math.sqrt(3); // 六边形宽度
    let instanceIndex = 0;

    // 蜂窝网络布局
    for (let row = -30; row <= 30; row++) { // 行数
      for (let col = -60; col <= 60; col++) { // 列数
        // 计算六边形中心位置
        let lat = row * hexHeight;
        let lon = col * hexWidth;

        // 奇数行偏移半个宽度，形成蜂窝结构
        if (row % 2 !== 0) {
          lon += hexWidth * 0.5;
        }

        // 跳过超出范围的cell
        if (lat < -60 || lat > 60 || lon < -180 || lon > 180) {
          continue;
        }

        // 创建六边形cell
        const cellCenter = Cesium.Cartesian3.fromDegrees(lon, lat);
        const hexVertices = createHexagonVertices(lon, lat, cellSize);

        // 创建多边形几何
        const polygonGeometry = new Cesium.PolygonGeometry({
          polygonHierarchy: new Cesium.PolygonHierarchy(hexVertices),
          height: 0,
          extrudedHeight: 1000
        });

        // 创建几何实例
        const geometryInstance = new Cesium.GeometryInstance({
          geometry: polygonGeometry,
          id: `cell_${row}_${col}`,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.CYAN.withAlpha(0.3))
          }
        });

        geometryInstancesRef.current.push(geometryInstance);

        const cellInfo: CellInfo = {
          id: `cell_${row}_${col}`,
          center: cellCenter,
          vertices: hexVertices,
          isIlluminated: false,
          index: instanceIndex
        };

        cellsRef.current.push(cellInfo);
        instanceIndex++;
      }
    }

    // 创建图元
    cellPrimitiveRef.current = new Cesium.Primitive({
      geometryInstances: geometryInstancesRef.current,
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: true,
        closed: false
      }),
      show: showCells
    });

    viewer.scene.primitives.add(cellPrimitiveRef.current);

    console.log(`创建了 ${cellsRef.current.length} 个cells`);
  };

  // 创建六边形顶点 - 标准蜂窝形状
  const createHexagonVertices = (centerLon: number, centerLat: number, size: number): any[] => {
    const vertices: any[] = [];
    const radius = size / 111000; // 转换为度数

    for (let i = 0; i < 6; i++) {
      // 从30度开始，确保六边形的一个边是水平的
      const angle = (i * 60 + 30) * Math.PI / 180;
      const lat = centerLat + radius * Math.sin(angle);
      const lon = centerLon + radius * Math.cos(angle) / Math.cos(centerLat * Math.PI / 180);
      vertices.push(Cesium.Cartesian3.fromDegrees(lon, lat));
    }

    return vertices;
  };

  // 创建卫星
  const createSatellite = () => {
    if (!viewerRef.current) return;

    const viewer = viewerRef.current;
    
    // 创建卫星轨道
    const startTime = Cesium.JulianDate.now();
    const stopTime = Cesium.JulianDate.addSeconds(startTime, 5400, new Cesium.JulianDate()); // 90分钟轨道

    const satellitePosition = new Cesium.SampledPositionProperty();
    
    // 创建轨道位置采样点
    for (let i = 0; i <= 5400; i += 60) { // 每分钟一个采样点
      const time = Cesium.JulianDate.addSeconds(startTime, i, new Cesium.JulianDate());
      const longitude = -180 + (i / 5400) * 360; // 90分钟绕地球一圈
      const latitude = 30 * Math.sin((i / 5400) * 2 * Math.PI); // 正弦波轨道
      const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, satelliteHeight);
      satellitePosition.addSample(time, position);
    }

    // 创建卫星实体
    satelliteRef.current = viewer.entities.add({
      id: 'satellite',
      position: satellitePosition,
      billboard: {
        image: '/images/statellite.png',
        scale: 0.5,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER
      },
      label: {
        text: '卫星',
        font: '14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -40)
      }
    });

    // 创建波束覆盖圆锥
    createBeamCone();

    // 设置时钟
    viewer.clock.startTime = startTime;
    viewer.clock.stopTime = stopTime;
    viewer.clock.currentTime = startTime;
    viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
    viewer.clock.multiplier = 10;
    viewer.clock.shouldAnimate = true;

    // 监听时钟变化，更新波束照射区域
    viewer.clock.onTick.addEventListener(updateBeamIllumination);
  };

  // 创建波束圆锥
  const createBeamCone = () => {
    if (!viewerRef.current || !satelliteRef.current) return;

    const viewer = viewerRef.current;

    beamRef.current = viewer.entities.add({
      id: 'beam',
      position: satelliteRef.current.position,
      cylinder: {
        length: satelliteHeight * 2,
        topRadius: 0,
        bottomRadius: new Cesium.ConstantProperty(beamRadius),
        material: new Cesium.ColorMaterialProperty(Cesium.Color.ORANGE.withAlpha(0.3)),
        outline: true,
        outlineColor: new Cesium.ConstantProperty(Cesium.Color.ORANGE),
        show: showBeam
      }
    });
  };

  // 更新波束照射区域 - 使用图元方式
  const updateBeamIllumination = () => {
    if (!viewerRef.current || !satelliteRef.current || !beamRef.current || !cellPrimitiveRef.current) return;

    const viewer = viewerRef.current;
    const currentTime = viewer.clock.currentTime;

    // 获取卫星当前位置
    const satellitePosition = satelliteRef.current.position?.getValue(currentTime);
    if (!satellitePosition) return;

    // 计算地面投影点
    const ellipsoid = viewer.scene.globe.ellipsoid;
    const groundPosition = ellipsoid.scaleToGeodeticSurface(satellitePosition);
    if (!groundPosition) return;

    let illuminatedCount = 0;
    const colorAttributes: any[] = [];

    // 检查每个cell是否在波束覆盖范围内
    cellsRef.current.forEach(cell => {
      const distance = Cesium.Cartesian3.distance(cell.center, groundPosition);
      const isIlluminated = distance <= beamRadius;

      if (cell.isIlluminated !== isIlluminated) {
        cell.isIlluminated = isIlluminated;
      }

      if (isIlluminated) {
        illuminatedCount++;
        // 高亮颜色 - 红色
        colorAttributes.push({
          index: cell.index,
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED.withAlpha(0.6))
        });
      } else {
        // 默认颜色 - 青色
        colorAttributes.push({
          index: cell.index,
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.CYAN.withAlpha(0.3))
        });
      }
    });

    // 批量更新颜色属性
    colorAttributes.forEach(attr => {
      const geometryInstance = geometryInstancesRef.current[attr.index];
      if (geometryInstance && cellPrimitiveRef.current) {
        // 直接更新图元的颜色属性
        const attributes = cellPrimitiveRef.current.getGeometryInstanceAttributes(geometryInstance.id);
        if (attributes && attributes.color) {
          attributes.color = attr.color.value;
        }
      }
    });

    setIlluminatedCellsCount(illuminatedCount);
  };

  // 更新cell大小
  const handleCellSizeChange = (value: number | null) => {
    if (value !== null) {
      setCellSize(value);
      createEarthCells();
      createSatellite();
    }
  };

  // 更新波束半径
  const handleBeamRadiusChange = (value: number | null) => {
    if (value !== null) {
      setBeamRadius(value);
      if (beamRef.current && beamRef.current.cylinder) {
        beamRef.current.cylinder.bottomRadius = new Cesium.ConstantProperty(value);
      }
    }
  };

  // 切换cells显示
  const toggleCellsVisibility = (visible: boolean) => {
    setShowCells(visible);
    if (cellPrimitiveRef.current) {
      cellPrimitiveRef.current.show = visible;
    }
  };

  // 切换波束显示
  const toggleBeamVisibility = (visible: boolean) => {
    setShowBeam(visible);
    if (beamRef.current) {
      beamRef.current.show = visible;
    }
  };

  return (
    <div style={{ height: '100vh', width: '100vw', position: 'relative' }}>
      <div ref={cesiumContainerRef} style={{ height: '100%', width: '100%' }} />
      
      {/* 控制面板 */}
      <Card 
        title="波束Cell覆盖控制面板" 
        style={{ 
          position: 'absolute', 
          top: 20, 
          left: 20, 
          width: 350,
          zIndex: 1000 
        }}
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div>Cell大小 (km): {(cellSize / 1000).toFixed(0)}</div>
            <Slider
              min={200000}
              max={1000000}
              step={50000}
              value={cellSize}
              onChange={handleCellSizeChange}
            />
          </Col>
          
          <Col span={24}>
            <div>波束半径 (km): {(beamRadius / 1000).toFixed(0)}</div>
            <Slider
              min={300000}
              max={1500000}
              step={50000}
              value={beamRadius}
              onChange={handleBeamRadiusChange}
            />
          </Col>
          
          <Col span={12}>
            <div>显示Cells:</div>
            <Switch checked={showCells} onChange={toggleCellsVisibility} />
          </Col>
          
          <Col span={12}>
            <div>显示波束:</div>
            <Switch checked={showBeam} onChange={toggleBeamVisibility} />
          </Col>
          
          <Col span={24}>
            <div style={{ 
              padding: '10px', 
              backgroundColor: '#f0f0f0', 
              borderRadius: '4px',
              textAlign: 'center'
            }}>
              <strong>被照射的Cells: {illuminatedCellsCount}</strong>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default BeamCellDemo;
