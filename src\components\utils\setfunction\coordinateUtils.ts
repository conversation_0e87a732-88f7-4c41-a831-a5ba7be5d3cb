// @ts-nocheck
// import * as Cesium from 'cesium';

// 笛卡尔坐标系转经纬度
export const GetWGS84FromDKR = (coor: any, type: number) => {
  let cartographic = Cesium.Cartographic.fromCartesian(coor);
  let x = Cesium.Math.toDegrees(cartographic.longitude);
  let y = Cesium.Math.toDegrees(cartographic.latitude);

  if (type === 0) return `(经度 :${x.toFixed(2)}, 纬度 : ${y.toFixed(2)})`;
  else if (type === 1) return [x as number, y as number];
};

// 经纬度转笛卡尔坐标
export const wgs84ToCartesign = (lng: any, lat: any, alt: any, viewer: any) => {
  var ellipsoid = viewer.scene.globe.ellipsoid;
  var cartographic = Cesium.Cartographic.fromDegrees(lng, lat, alt);
  var cartesian3 = ellipsoid.cartographicToCartesian(cartographic);

  return cartesian3;
}; 