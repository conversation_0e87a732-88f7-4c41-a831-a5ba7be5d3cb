import React from 'react';
import Box from '../components/main/box';
import CoreNetworkLogs from '../components/right/CoreNetwork/CoreNetworkLogs';
import '../components/main/css/box.css';
import '../components/main/css/cesium.css';

const CoreNetworkLogsPreview: React.FC = () => {
  return (
    <div style={{ 
      background: 'linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%)', 
      minHeight: '100vh', 
      padding: '20px' 
    }}>
      <h1 style={{ color: '#ffffff', textAlign: 'center', marginBottom: '30px' }}>
        核心网日志组件样式预览
      </h1>
      
      <div style={{ 
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <Box 
          title="核心网日志" 
          component={<CoreNetworkLogs />} 
        />
      </div>
      
      <div style={{ 
        marginTop: '30px',
        color: '#ffffff',
        textAlign: 'center',
        fontSize: '14px'
      }}>
        <p>点击不同的标签页查看各个网络功能的日志信息</p>
        <p>AMF: 接入和移动性管理功能</p>
        <p>SMF: 会话管理功能</p>
        <p>UPF: 用户面功能</p>
        <p>PCF: 策略控制功能</p>
        <p>UDM: 统一数据管理</p>
        <p>NRF: 网络存储功能</p>
      </div>
    </div>
  );
};

export default CoreNetworkLogsPreview; 