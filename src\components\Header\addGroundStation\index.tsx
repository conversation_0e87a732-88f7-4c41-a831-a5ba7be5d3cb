import React, { useState } from 'react';
import { Modal, Tabs, Form, Input, Button, Upload, InputNumber, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import styles from './style.module.css';
import { 
  uploadGroundStationFile, 
  createGroundStationFromTemplate, 
  validateGroundStationData,
  type GroundStationTemplateData 
} from './groundStationAPI';

const { TabPane } = Tabs;
const { Dragger } = Upload;

interface AddGroundStationProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface FileImportData {
  groundStationName: string;
  groundStationFile: UploadFile | null;
}

interface TemplateData {
  groundStationName: string;
  longitude: number;
  latitude: number;
  description?: string;
}

const AddGroundStation: React.FC<AddGroundStationProps> = ({
  visible,
  onClose,
  onSubmit
}) => {
  const [activeTab, setActiveTab] = useState<string>('file');
  const [fileImportForm] = Form.useForm<FileImportData>();
  const [templateForm] = Form.useForm<TemplateData>();
  const [groundStationFile, setGroundStationFile] = useState<UploadFile | null>(null);

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: false,
    beforeUpload: (file: UploadFile) => {
      return false; // 阻止自动上传
    },
  };

  // 地面站文件上传处理
  const handleGroundStationFileUpload = (info: any) => {
    const { file } = info;
    if (file.name.endsWith('.json') || file.name.endsWith('.txt') || file.name.endsWith('.csv')) {
      setGroundStationFile(file);
      message.success(`${file.name} 文件上传成功`);
    } else {
      message.error('请上传 .json, .txt 或 .csv 格式的文件');
    }
  };

  // 文件导入提交
  const handleFileImportSubmit = async () => {
    if (!groundStationFile) {
      message.error('请上传地面站文件');
      return;
    }

    // 构建FormData
    const formData = new FormData();
    formData.append('groundStationFile', groundStationFile as any);

    try {
      message.loading('正在上传地面站文件...', 0);
      
      const result = await uploadGroundStationFile(formData);
      
      message.destroy(); // 清除loading消息
      
      if (result.success) {
        message.success(result.message || '地面站文件上传成功');
        onSubmit({
          type: 'file',
          success: true,
          data: result
        });
        handleClose();
      } else {
        message.error(result.error || '地面站文件上传失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`上传失败: ${error.message}`);
      console.error('地面站文件上传错误:', error);
    }
  };

  // 模版构建提交
  const groundhandleTemplateSubmit = async (values: TemplateData) => {
    const apiData: GroundStationTemplateData = {
      name: values.groundStationName,
      longitude: values.longitude,
      latitude: values.latitude,
      altitude : 0
      // description: values.description || ''
    };

    // 验证数据
    const validation = validateGroundStationData(apiData);
    if (!validation.isValid) {
      message.error(validation.error);
      return;
    }

    try {
      message.loading('正在创建地面站...', 0);
      
      const result = await createGroundStationFromTemplate(apiData);
      
      message.destroy(); // 清除loading消息
      
      if (result.success) {
        console.log( '地面站创建成功',result.message)


        // message.success(result.message || '地面站创建成功');
        // onSubmit({
        //   type: 'template',
        //   success: true,
        //   data: result,
        //   config: apiData
        // });
        // handleClose();
      } else {
        message.error(result.error || '地面站创建失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`创建失败: ${error.message}`);
      console.error('地面站模版构建错误:', error);
    }
  };

  // 关闭处理
  const handleClose = () => {
    fileImportForm.resetFields();
    templateForm.resetFields();
    setGroundStationFile(null);
    setActiveTab('file');
    onClose();
  };

  return (
    <Modal
      title="地面站设计"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      centered
      className={styles.addGroundStationModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="地面站文件导入" key="file">
          <Form
            form={fileImportForm}
            layout="vertical"
            onFinish={() => handleFileImportSubmit()}
            className={styles.form}
          >

            <Form.Item
              label="地面站文件"
              rules={[{ required: true, message: '请上传地面站文件' }]}
            >
              <Dragger
                {...uploadProps}
                onChange={handleGroundStationFileUpload}
                accept=".json,.txt,.csv"
                className={styles.uploader}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  {groundStationFile ? groundStationFile.name : '点击或拖拽地面站文件到此区域'}
                </p>
                <p className="ant-upload-hint">
                  支持 .json, .txt 和 .csv 格式文件
                </p>
              </Dragger>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" className={styles.submitButton}>
                导入地面站
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="地面站模版构建" key="groundStationTemplate">
          <Form
            form={templateForm}
            layout="vertical"
            onFinish={groundhandleTemplateSubmit}
            className={styles.form}
          >
            <Form.Item
              name="groundStationName"
              label="地面站名称"
              rules={[{ required: true, message: '请输入地面站名称' }]}
            >
              <Input
                placeholder="请输入地面站名称"
              />
            </Form.Item>

            <Form.Item
              name="longitude"
              label="经度"
              rules={[{ required: true, message: '请输入经度' }]}
            >
              <InputNumber
                min={-180}
                max={180}
                step={0.000001}
                precision={6}
                placeholder="请输入经度（-180到180）"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="latitude"
              label="纬度"
              rules={[{ required: true, message: '请输入纬度' }]}
            >
              <InputNumber
                min={-90}
                max={90}
                step={0.000001}
                precision={6}
                placeholder="请输入纬度（-90到90）"
                style={{ width: '100%' }}
              />
            </Form.Item>

         

            {/* <Form.Item
              name="description"
              label="描述"
            >
              <Input.TextArea
                rows={3}
                placeholder="请输入地面站描述（可选）"
              />
            </Form.Item> */}

            <Form.Item>
              <Button type="primary" htmlType="submit" className={styles.submitButton}>
                创建地面站
              </Button>
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>
    </Modal>
  );
};



export default AddGroundStation; 