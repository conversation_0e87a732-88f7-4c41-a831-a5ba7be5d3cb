.addSatelliteModal {
  color: #fff !important;
}

/* 全局强制白色文字 */
.addSatelliteModal * {
  color: #fff !important;
}

/* 保持按钮和特殊元素的颜色 */
.addSatelliteModal .ant-btn-primary {
  color: #fff !important;
}

.addSatelliteModal .ant-tabs-tab-active,
.addSatelliteModal .ant-tabs-tab-active * {
  color: #1890ff !important;
}

.addSatelliteModal .ant-upload-drag-icon * {
  color: #1890ff !important;
}

/* .addSatelliteModal .ant-modal-content {
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
} */

.addSatelliteModal .ant-modal {
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.addSatelliteModal .ant-modal-header {
  background-color: #1e1e1e;
  border-bottom: 1px solid #333;
}

.addSatelliteModal .ant-modal-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.addSatelliteModal .ant-modal-close-x {
  color: #fff!important;
}

.addSatelliteModal .ant-modal-body {
    
  background-color: #1e1e1e;
  padding: 20px;
}

.addSatelliteModal .ant-tabs-tab {
  color: #fff !important;
}

.addSatelliteModal .ant-tabs-tab-active {
  color: #1890ff !important;
}

.addSatelliteModal .ant-tabs-tab:hover {
  color: #1890ff !important;
}

.addSatelliteModal .ant-tabs-ink-bar {
  background-color: #1890ff;
}

.addSatelliteModal .ant-tabs-content-holder {
  background-color: #1e1e1e;
}

.form {
  margin-top: 20px;
}

.form .ant-form-item-label > label {
  color: #fff !important;
  font-weight: 500;
}


.form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

.form input[placeholder*="星座名称"] {
  background: transparent !important;
  border: 1px solid #fff !important;
  color: #fff !important;
}

.addSatelliteModal .form .ant-input {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #000 !important;
}

.form .ant-input:focus {
  background-color: #2a2a2a !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.form .ant-input:hover {
  background-color: #2a2a2a !important;
  border-color: #666 !important;
}

/* 特别针对TLE URL输入框 */
.addSatelliteModal .form .ant-input {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #000 !important;
}

.addSatelliteModal .form .ant-input:focus {
  background-color: #2a2a2a !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.addSatelliteModal .form .ant-input:hover {
  background-color: #2a2a2a !important;
  border-color: #666 !important;
}

.form .ant-input-number {
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: #fff;
  width: 100%;
}

.form .ant-input-number:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.addSatelliteModal .form .ant-input-number .ant-input-number-input {
  background-color: transparent;
  color: #000;
}

.form .ant-select {
  background-color: #2a2a2a;
}

.form .ant-select .ant-select-selector {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #fff !important;
}

.form .ant-select .ant-select-selection-item {
  color: #fff !important;
}

.form .ant-select .ant-select-arrow {
  color: #fff;
}

.form .ant-select:focus .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.uploader {
  background-color: #2a2a2a !important;
  border: 2px dashed #444 !important;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.uploader:hover {
  border-color: #1890ff !important;
}

.uploader .ant-upload-drag-icon {
  color: #1890ff !important;
}

.uploader .ant-upload-text {
  color: #fff !important;
  font-size: 14px;
  font-weight: 500;
}

.uploader .ant-upload-hint {
  color: #fff !important;
  font-size: 12px;
}

.submitButton {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
  width: 100%;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  margin-top: 10px;
}

.submitButton:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.submitButton:focus {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 下拉框选项样式 */
.form .ant-select-dropdown {
  background-color: #2a2a2a;
  border: 1px solid #444;
}

.form .ant-select-item {
  color: #fff;
}

.form .ant-select-item:hover {
  background-color: #333;
}

.form .ant-select-item-option-selected {
  background-color: #1890ff;
  color: #fff;
}

/* 浏览器自动填充样式覆盖 - 需要更高优先级 */
.addSatelliteModal .form .ant-input:-webkit-autofill,
.addSatelliteModal .form .ant-input:-webkit-autofill:hover,
.addSatelliteModal .form .ant-input:-webkit-autofill:focus,
.addSatelliteModal .form .ant-input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #2a2a2a inset !important;
  -webkit-text-fill-color: #000 !important;
  background-color: #2a2a2a !important;
  color: #000 !important;
}

/* 确保输入框文字始终为黑色 - 需要更高优先级覆盖全局白色 */
.addSatelliteModal .form .ant-input,
.addSatelliteModal .form .ant-input-number-input {
  -webkit-text-fill-color: #000 !important;
  color: #000 !important;
}

/* 消息提示框样式 */
.ant-message .ant-message-notice {
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: #fff;
}

.ant-message .ant-message-success .anticon {
  color: #52c41a;
}

.ant-message .ant-message-error .anticon {
  color: #ff4d4f;
}
